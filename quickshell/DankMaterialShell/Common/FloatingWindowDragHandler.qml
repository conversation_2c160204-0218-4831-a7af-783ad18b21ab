import QtQuick
import QtQuick.Controls
import qs.Common

/**
 * 🇺🇸 AMERICA'S FLOATING WINDOW DRAG HANDLER - FREEDOM TO MOVE!
 * 
 * This component provides drag functionality for floating windows.
 * It allows users to drag popouts and modals around the screen.
 */
MouseArea {
    id: dragHandler
    
    // Properties for drag behavior
    property bool enableDrag: true
    property bool dragActive: false
    property point dragStartPos: Qt.point(0, 0)
    property point windowStartPos: Qt.point(0, 0)
    property real dragThreshold: 5 // Minimum distance to start dragging
    property bool hasMoved: false
    
    // Target window to drag (should be the parent window)
    property var targetWindow: parent
    
    // Visual feedback properties
    property bool showDragFeedback: true
    property real feedbackOpacity: 0.8
    property real feedbackScale: 1.02
    
    // Constraints
    property bool constrainToScreen: true
    property real screenMargin: 10
    
    // Cursor
    cursorShape: enableDrag ? (dragActive ? Qt.ClosedHandCursor : Qt.OpenHandCursor) : Qt.ArrowCursor
    
    // Store original window properties for restoration
    property real originalOpacity: 1.0
    property real originalScale: 1.0
    
    onPressed: (mouse) => {
        if (!enableDrag) return
        
        dragStartPos = Qt.point(mouse.x, mouse.y)
        if (targetWindow) {
            windowStartPos = Qt.point(targetWindow.x, targetWindow.y)
            originalOpacity = targetWindow.opacity || 1.0
            originalScale = targetWindow.scale || 1.0
        }
        hasMoved = false
    }
    
    onPositionChanged: (mouse) => {
        if (!enableDrag || !pressed) return
        
        var deltaX = mouse.x - dragStartPos.x
        var deltaY = mouse.y - dragStartPos.y
        var distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
        
        // Start dragging only after threshold is exceeded
        if (!dragActive && distance > dragThreshold) {
            dragActive = true
            hasMoved = true
            
            // Apply visual feedback
            if (showDragFeedback && targetWindow) {
                targetWindow.opacity = feedbackOpacity
                if (targetWindow.scale !== undefined) {
                    targetWindow.scale = feedbackScale
                }
            }
        }
        
        if (dragActive && targetWindow) {
            var newX = windowStartPos.x + deltaX
            var newY = windowStartPos.y + deltaY
            
            // Constrain to screen if enabled
            if (constrainToScreen && targetWindow.screen) {
                var screen = targetWindow.screen
                var windowWidth = targetWindow.width || targetWindow.implicitWidth || 0
                var windowHeight = targetWindow.height || targetWindow.implicitHeight || 0
                
                newX = Math.max(screenMargin, 
                       Math.min(screen.width - windowWidth - screenMargin, newX))
                newY = Math.max(screenMargin, 
                       Math.min(screen.height - windowHeight - screenMargin, newY))
            }
            
            // For PanelWindow, we need to handle positioning differently
            // since they use anchors. We'll store the offset and let the 
            // window handle positioning in its own coordinate system.
            if (targetWindow.hasOwnProperty('dragOffsetX')) {
                targetWindow.dragOffsetX = newX - windowStartPos.x
                targetWindow.dragOffsetY = newY - windowStartPos.y
            } else {
                // Fallback for regular Items
                targetWindow.x = newX
                targetWindow.y = newY
            }
        }
    }
    
    onReleased: (mouse) => {
        if (!enableDrag) return
        
        if (dragActive) {
            dragActive = false
            
            // Restore visual feedback
            if (showDragFeedback && targetWindow) {
                // Animate back to normal appearance
                opacityAnimation.to = originalOpacity
                opacityAnimation.start()
                
                if (targetWindow.scale !== undefined) {
                    scaleAnimation.to = originalScale
                    scaleAnimation.start()
                }
            }
        }
        
        // Reset drag state after a short delay to allow click detection
        Qt.callLater(() => {
            hasMoved = false
        })
    }
    
    // Animations for visual feedback
    NumberAnimation {
        id: opacityAnimation
        target: targetWindow
        property: "opacity"
        duration: 200
        easing.type: Easing.OutCubic
    }
    
    NumberAnimation {
        id: scaleAnimation
        target: targetWindow
        property: "scale"
        duration: 200
        easing.type: Easing.OutCubic
    }
    
    // Prevent clicks when dragging
    onClicked: (mouse) => {
        if (hasMoved) {
            mouse.accepted = true
        }
    }
}
