name: Create Release
on:
  push:
    tags:
      - 'v*'

concurrency:
  group: release-${{ github.ref_name }}
  cancel-in-progress: true

jobs:
  create_release:
    name: 📦 Create GitHub Release
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch full history for changelog generation

      # Generate changelog
      - name: Generate Changelog
        id: changelog
        run: |
          # Get the previous tag
          PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")

          if [ -z "$PREVIOUS_TAG" ]; then
            echo "No previous tag found, using all commits"
            CHANGELOG=$(git log --oneline --pretty=format:"- %s (%h)" | head -50)
          else
            echo "Generating changelog from $PREVIOUS_TAG to HEAD"
            CHANGELOG=$(git log --oneline --pretty=format:"- %s (%h)" $PREVIOUS_TAG..HEAD)
          fi

          # Create the changelog with proper formatting
          cat > CHANGELOG.md << EOF
          ## What's Changed

          $CHANGELOG

          **Full Changelog**: https://github.com/${{ github.repository }}/compare/${PREVIOUS_TAG}...${{ github.ref_name }}
          EOF

          # Set output for use in release step
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          cat CHANGELOG.md >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      # Create GitHub Release
      - name: Create GitHub Release
        uses: comnoco/create-release-action@v2.0.5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref_name }}
          release_name: Release ${{ github.ref_name }}
          body: ${{ steps.changelog.outputs.changelog }}
          draft: false
          prerelease: ${{ contains(github.ref_name, '-') }}
