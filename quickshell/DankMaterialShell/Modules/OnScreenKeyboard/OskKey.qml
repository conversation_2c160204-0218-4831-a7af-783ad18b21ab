import QtQuick
import QtQuick.Layouts
import "../../Common"
import "../../Services"
import "../../Widgets"

StyledRect {
    id: root
    property var keyData
    property string key: keyData.label
    property string type: keyData.keytype
    property var keycode: keyData.keycode
    property string shape: keyData.shape
    property bool isShift: YdotoolService.shiftKeys.includes(keycode)
    property bool isBackspace: (key.toLowerCase() == "backspace")
    property bool isEnter: (key.toLowerCase() == "enter" || key.toLowerCase() == "return")
    property real baseWidth: 45
    property real baseHeight: 45
    property var widthMultiplier: ({
        "normal": 1,
        "fn": 1,
        "tab": 1.6,
        "caps": 1.9,
        "shift": 2.5,
        "control": 1.3,
        "space": 4.0,
        "expand": 2.0
    })
    property var heightMultiplier: ({
        "normal": 1,
        "fn": 0.7,
        "tab": 1,
        "caps": 1,
        "shift": 1,
        "control": 1
    })
    property bool toggled: isShift ? YdotoolService.shiftMode : modkeyToggled
    property bool modkeyToggled: false

    enabled: shape != "empty"
    visible: shape != "empty"

    // 🚨 FORCE LAYOUT CONTROL - AMERICA DEMANDS RESPONSIVE BUTTONS!
    implicitWidth: baseWidth * (widthMultiplier[shape] || 1)
    implicitHeight: baseHeight * (heightMultiplier[shape] || 1)

    Layout.fillWidth: true
    Layout.fillHeight: true
    Layout.preferredWidth: baseWidth * (widthMultiplier[shape] || 1)
    Layout.preferredHeight: baseHeight * (heightMultiplier[shape] || 1)
    Layout.minimumWidth: Math.max(20, baseWidth * (widthMultiplier[shape] || 1) * 0.5)
    Layout.minimumHeight: Math.max(15, baseHeight * (heightMultiplier[shape] || 1) * 0.5)
    Layout.maximumHeight: baseHeight * (heightMultiplier[shape] || 1) * 2.0
    
    color: toggled ? Theme.primary :
           Qt.rgba(Theme.surfaceVariant.r, Theme.surfaceVariant.g, Theme.surfaceVariant.b, 0.8)
    radius: Theme.cornerRadius
    border.width: 1
    border.color: Theme.outlineMedium

    Connections {
        target: YdotoolService
        enabled: isShift
        function onShiftModeChanged() {
            if (YdotoolService.shiftMode == 0) {
                capsLockTimer.hasStarted = false;
            }
        }
    }

    Timer {
        id: capsLockTimer
        property bool hasStarted: false
        property bool canCaps: false
        interval: 300
        function startWaiting() {
            hasStarted = true;
            canCaps = true;
            start();
        }
        onTriggered: {
            canCaps = false;
        }
    }

    StyledText {
        id: keyText
        anchors.fill: parent
        // 🇺🇸 AMERICA DEMANDS SCALABLE FONTS!
        font.pixelSize: {
            var baseFontSize = root.shape == "fn" ? 12 :
                (isBackspace || isEnter) ? 18 : 14
            // Scale font size based on button height
            var heightRatio = root.baseHeight / 45 // 45 is the default baseHeight
            return Math.max(8, baseFontSize * heightRatio)
        }
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
        color: root.toggled ? Theme.primaryText : Theme.surfaceVariantText
        text: root.isBackspace ? "⌫" : root.isEnter ? "↵" :
            YdotoolService.shiftMode == 2 ? (root.keyData.labelCaps || root.keyData.labelShift || root.keyData.label) :
            YdotoolService.shiftMode == 1 ? (root.keyData.labelShift || root.keyData.label) :
            root.keyData.label
    }

    StateLayer {
        stateColor: Theme.primary
        cornerRadius: root.radius
        
        onPressed: {
            YdotoolService.press(root.keycode);
            if (isShift && YdotoolService.shiftMode == 0) YdotoolService.shiftMode = 1;
        }
        
        onReleased: {
            if (root.type == "normal") {
                YdotoolService.release(root.keycode);
                if (YdotoolService.shiftMode == 1) {
                    YdotoolService.releaseShiftKeys()
                }
            } else if (isShift) {
                if (YdotoolService.shiftMode == 1) {
                    if (!capsLockTimer.hasStarted) {
                        capsLockTimer.startWaiting();
                    } else {
                        if (capsLockTimer.canCaps) {
                            YdotoolService.shiftMode = 2; // Caps lock mode
                        } else {
                            YdotoolService.releaseShiftKeys()
                        }
                    }
                } else if (YdotoolService.shiftMode == 2) {
                    YdotoolService.releaseShiftKeys();
                }
            } else if (root.type == "modkey") {
                if (!isShift) {
                    root.modkeyToggled = !root.modkeyToggled;
                    if (!root.modkeyToggled) {
                        YdotoolService.release(root.keycode);
                    }
                }
            }
        }
    }
}
