import QtQuick
import qs.Common

Rectangle {
  id: root

  color: "transparent"
  radius: Appearance.rounding.normal

  Behavior on color {
    ColorAnimation {
      duration: Appearance.anim.durations.normal
      easing.type: Easing.BezierSpline
      easing.bezierCurve: Appearance.anim.curves.standard
    }
  }

  Behavior on radius {
    NumberAnimation {
      duration: Appearance.anim.durations.normal
      easing.type: Easing.BezierSpline
      easing.bezierCurve: Appearance.anim.curves.standard
    }
  }

  Behavior on opacity {
    NumberAnimation {
      duration: Appearance.anim.durations.normal
      easing.type: Easing.BezierSpline
      easing.bezierCurve: Appearance.anim.curves.standard
    }
  }
}
