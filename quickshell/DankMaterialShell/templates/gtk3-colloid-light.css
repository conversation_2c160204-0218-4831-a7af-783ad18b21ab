@import url("dank-colors.css")

@keyframes ripple {
  to {
    background-size: 1000% 1000%;
  }
}

@keyframes ripple-on-slider {
  to {
    background-size: auto, 1000% 1000%;
  }
}

@keyframes ripple-on-headerbar {
  from {
    background-image: radial-gradient(circle, @accent_color 0%, transparent 0%);
  }
  to {
    background-image: radial-gradient(circle, @accent_color 100%, transparent 0%);
  }
}

* {
  background-clip: padding-box;
  -GtkToolButton-icon-spacing: 0;
  -GtkTextView-error-underline-color: #E53935;
  -GtkScrolledWindow-scrollbar-spacing: 0;
  -GtkToolItemGroup-expander-size: 11;
  -GtkWidget-text-handle-width: 24;
  -GtkWidget-text-handle-height: 24;
  -GtkDialog-button-spacing: 6;
  -GtkDialog-action-area-border: 6;
  outline-style: solid;
  outline-width: 2px;
  outline-color: transparent;
  outline-offset: -4px;
  -gtk-outline-radius: 6px;
  -gtk-secondary-caret-color: @accent_color;
}

*:focus {
  outline-color: alpha(currentColor, 0.1);
}

XfdesktopIconView.view:active, calendar.raven-calendar:selected, box.vertical > widget > widget:selected, calendar:selected, popover.background modelbutton.flat:selected,
popover.background .menuitem.button.flat:selected, .csd treeview.view:selected, .background.csd .view:selected {
  color: rgba(0, 0, 0, 0.87);
  background-color: alpha(currentColor, 0.1);
}

.nemo-window .view selection, .nemo-window .view:selected, .nautilus-window notebook .view:not(treeview) selection, .nautilus-window notebook .view:not(treeview):selected, .nautilus-window flowboxchild:selected .icon-item-background, label selection, flowbox flowboxchild:selected {
  color: @accent_color;
  background-color: alpha(@accent_color, 0.2);
}

.nemo-window .nemo-window-pane widget.entry:selected, window.background.csd evview.view.content-view:selected, window.background.csd evview.view.content-view:selected:backdrop, .nautilus-window.background.csd notebook widget.view:selected, entry selection, textview text selection:focus, textview text selection, widget.view:selected, .view:selected {
  color: #FFFFFF;
  background-color: @accent_color;
}

.linked:not(.vertical) > button, .linked:not(.vertical) > entry {
  border-radius: 0;
}

.linked:not(.vertical) > button:first-child, .linked:not(.vertical) > entry:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.linked:not(.vertical) > button:last-child, .linked:not(.vertical) > entry:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.linked:not(.vertical) > button:only-child, .linked:not(.vertical) > entry:only-child {
  border-radius: 6px;
}

.linked.vertical > button, .linked.vertical > entry {
  border-radius: 0;
}

.linked.vertical > button:first-child, .linked.vertical > entry:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.linked.vertical > button:last-child, .linked.vertical > entry:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.linked.vertical > button:only-child, .linked.vertical > entry:only-child {
  border-radius: 6px;
}

/***************
 * Base States *
 ***************/
.background {
  background-color: @window_bg_color;
  color: @window_fg_color;
}

.background.csd {
  border-radius: 0 0 12px 12px;
}

.background.maximized, .background.solid-csd {
  border-radius: 0;
}

*:disabled {
  -gtk-icon-effect: dim;
}

.gtkstyle-fallback {
  background-color: #242424;
  color: #FFFFFF;
}

.gtkstyle-fallback:hover {
  background-color: #171717;
  color: #FFFFFF;
}

.gtkstyle-fallback:active {
  background-color: #0b0b0b;
  color: #FFFFFF;
}

.gtkstyle-fallback:disabled {
  background-color: #2C2C2C;
  color: rgba(255, 255, 255, 0.5);
}

.gtkstyle-fallback:selected {
  background-color: @accent_color;
  color: #FFFFFF;
}

.view {
  background-color: @view_bg_color;
  color: @view_fg_color;
}

.view:hover {
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.08);
}

.view:disabled {
  color: rgba(0, 0, 0, 0.38);
}

.view:selected:hover {
  box-shadow: none;
}

window.background.csd > stack.view {
  border-radius: 0 0 12px 12px;
}

textview text {
  background-color: @view_bg_color;
}

textview border {
  background-color: #FAFAFA;
  color: rgba(0, 0, 0, 0.6);
}

iconview:hover, iconview:selected {
  border-radius: 6px;
}

.content-view rubberband, .content-view .rubberband, treeview.view rubberband, treeview.view XfdesktopIconView.view .rubberband, XfdesktopIconView.view treeview.view .rubberband, flowbox rubberband,
.rubberband,
rubberband,
XfdesktopIconView.view .rubberband {
  border: 1px solid @accent_color;
  background-color: alpha(@accent_color, 0.3);
}

flowbox flowboxchild {
  padding: 3px;
  border-radius: 6px;
  color: rgba(0, 0, 0, 0.87);
}

flowbox flowboxchild button.osd.remove-button {
  min-height: 28px;
  min-width: 28px;
  padding: 0;
  margin: 6px;
}

.content-view .tile:selected {
  background-color: transparent;
}

label {
  caret-color: currentColor;
}

label.separator {
  color: rgba(0, 0, 0, 0.6);
}

label:disabled {
  color: rgba(0, 0, 0, 0.38);
}

headerbar label:disabled, tab label:disabled, button label:disabled {
  color: inherit;
}

label.osd {
  border-radius: 6px;
  background-color: rgba(52, 52, 52, 0.9);
  color: #FFFFFF;
}

.dim-label {
  color: rgba(0, 0, 0, 0.6);
}

assistant .sidebar {
  padding: 4px 0;
}

assistant .sidebar label {
  min-height: 34px;
  padding: 0 12px;
  color: rgba(0, 0, 0, 0.38);
  font-weight: 500;
}

assistant .sidebar label.highlight {
  color: rgba(0, 0, 0, 0.87);
}

/*********************
 * Spinner Animation *
 *********************/
@keyframes spin {
  to {
    -gtk-icon-transform: rotate(1turn);
  }
}

spinner {
  background: none;
  opacity: 0;
  -gtk-icon-source: -gtk-icontheme("process-working-symbolic");
}

spinner:checked {
  opacity: 1;
  animation: spin 1s linear infinite;
}

spinner:checked:disabled {
  opacity: 0.5;
}

/****************
 * Text Entries *
 ****************/
spinbutton, entry {
  min-height: 34px;
  padding: 0 8px;
  border-radius: 6px;
  caret-color: currentColor;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.87);
}

spinbutton:focus, entry:focus {
  background-color: rgba(0, 0, 0, 0.04);
  box-shadow: inset 0 0 0 2px @accent_color;
}

spinbutton:drop(active), entry:drop(active) {
  background-color: alpha(currentColor, 0.08);
  box-shadow: inset 0 0 0 2px alpha(currentColor, 0.08);
}

spinbutton:disabled, entry:disabled {
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.38);
}

entry.flat {
  min-height: 0;
  padding: 2px;
  border-radius: 0;
  background-color: transparent;
}

entry image {
  color: rgba(0, 0, 0, 0.6);
}

entry image:hover, entry image:active {
  color: rgba(0, 0, 0, 0.87);
}

entry image:disabled {
  color: rgba(0, 0, 0, 0.38);
}

entry image.left {
  margin-left: 1px;
  margin-right: 6px;
}

entry image.right {
  margin-left: 6px;
  margin-right: 1px;
}

entry undershoot.left {
  background-color: transparent;
  background-image: linear-gradient(to top, transparent 50%, rgba(0, 0, 0, 0.26) 50%);
  padding-left: 1px;
  background-size: 1px 12px;
  background-repeat: repeat-y;
  background-origin: content-box;
  background-position: left top;
  margin: 0 4px;
  margin: 4px 0;
}

entry undershoot.right {
  background-color: transparent;
  background-image: linear-gradient(to top, transparent 50%, rgba(0, 0, 0, 0.26) 50%);
  padding-right: 1px;
  background-size: 1px 12px;
  background-repeat: repeat-y;
  background-origin: content-box;
  background-position: right top;
  margin: 0 4px;
  margin: 4px 0;
}

entry.error {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.87);
}

entry.error:focus {
  background-color: rgba(0, 0, 0, 0.04);
  box-shadow: inset 0 0 0 2px #E53935;
}

entry.error:disabled {
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.38);
}

entry.error image {
  color: rgba(255, 255, 255, 0.7);
}

entry.error image:hover, entry.error image:active {
  color: #FFFFFF;
}

entry.error image:disabled {
  color: rgba(255, 255, 255, 0.5);
}

entry.warning {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.87);
}

entry.warning:focus {
  background-color: rgba(0, 0, 0, 0.04);
  box-shadow: inset 0 0 0 2px #FBC02D;
}

entry.warning:disabled {
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.38);
}

entry.warning image {
  color: rgba(0, 0, 0, 0.6);
}

entry.warning image:hover, entry.warning image:active {
  color: rgba(0, 0, 0, 0.87);
}

entry.warning image:disabled {
  color: rgba(0, 0, 0, 0.38);
}

entry progress {
  margin: 2px -8px;
  border-bottom: 2px solid @accent_color;
  background-color: transparent;
}

treeview entry.flat, treeview entry {
  background-color: #FFFFFF;
}

treeview entry.flat, treeview entry.flat:focus, treeview entry, treeview entry:focus {
  border-image: none;
  box-shadow: none;
}

.entry-tag, .photos-entry-tag, .documents-entry-tag {
  margin: 2px;
  border-radius: 9999px;
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.87);
}

.entry-tag:hover, .photos-entry-tag:hover, .documents-entry-tag:hover {
  background-image: image(alpha(currentColor, 0.08));
}

:dir(ltr) .entry-tag, :dir(ltr) .photos-entry-tag, :dir(ltr) .documents-entry-tag {
  margin-left: 4px;
  margin-right: 0;
  padding-left: 12px;
  padding-right: 8px;
}

:dir(rtl) .entry-tag, :dir(rtl) .photos-entry-tag, :dir(rtl) .documents-entry-tag {
  margin-left: 0;
  margin-right: 4px;
  padding-left: 8px;
  padding-right: 12px;
}

.entry-tag.button, .button.photos-entry-tag, .button.documents-entry-tag {
  box-shadow: none;
  background-color: transparent;
}

.entry-tag.button:not(:hover):not(:active), .button.photos-entry-tag:not(:hover):not(:active), .button.documents-entry-tag:not(:hover):not(:active) {
  color: rgba(0, 0, 0, 0.6);
}

/***********
 * Buttons *
 ***********/
@keyframes needs-attention {
  from {
    background-image: -gtk-gradient(radial, center center, 0, center center, 0.001, to(@accent_color), to(transparent));
  }
  to {
    background-image: -gtk-gradient(radial, center center, 0, center center, 0.5, to(@accent_color), to(transparent));
  }
}

.raven-mpris button.image-button, .mate-panel-menu-bar button, .nautilus-window .floating-bar button, infobar.warning > revealer > box button, infobar.warning:backdrop > revealer > box button {
  color: rgba(255, 255, 255, 0.7);
}

.raven-mpris button.image-button:focus, .mate-panel-menu-bar button:focus, .nautilus-window .floating-bar button:focus, infobar.warning > revealer > box button:focus, .raven-mpris button.image-button:hover, .mate-panel-menu-bar button:hover, .nautilus-window .floating-bar button:hover, infobar.warning > revealer > box button:hover, .raven-mpris button.image-button:active, .mate-panel-menu-bar button:active, .nautilus-window .floating-bar button:active, infobar.warning > revealer > box button:active, .raven-mpris button.image-button:checked, .mate-panel-menu-bar button:checked, .nautilus-window .floating-bar button:checked, infobar.warning > revealer > box button:checked {
  color: #FFFFFF;
}

.raven-mpris button.image-button:disabled, .mate-panel-menu-bar button:disabled, .nautilus-window .floating-bar button:disabled, infobar.warning > revealer > box button:disabled {
  color: rgba(255, 255, 255, 0.32);
}

.raven-mpris button.image-button:checked:disabled, .mate-panel-menu-bar button:checked:disabled, .nautilus-window .floating-bar button:checked:disabled, infobar.warning > revealer > box button:checked:disabled {
  color: rgba(255, 255, 255, 0.5);
}

actionbar > revealer > box .linked > button:not(.suggested-action):not(.destructive-action), button {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  outline: none;
  box-shadow: inset 0 0 0 9999px transparent;
  background-color: rgba(0, 0, 0, 0.04);
  background-image: radial-gradient(circle, transparent 10%, transparent 0%);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1000% 1000%;
  color: rgba(0, 0, 0, 0.87);
}

actionbar > revealer > box .linked > button:focus:not(.suggested-action):not(.destructive-action), button:focus {
  box-shadow: 0 0 0 2px alpha(@accent_color, 0.35);
}

actionbar > revealer > box .linked > button:hover:not(.suggested-action):not(.destructive-action), button:hover {
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.08);
}

actionbar > revealer > box .linked > button:active:not(.suggested-action):not(.destructive-action), button:active {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms, border 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.08);
  background-image: radial-gradient(circle, alpha(currentColor, 0.12) 10%, transparent 0%);
  background-size: 0% 0%;
}

actionbar > revealer > box .linked > button:disabled:not(.suggested-action):not(.destructive-action), button:disabled {
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.38);
}

actionbar > revealer > box .linked > button:checked:not(.suggested-action):not(.destructive-action), button:checked {
  background-color: @accent_color;
  color: #FFFFFF;
}

actionbar > revealer > box .linked > button:checked:hover:not(.suggested-action):not(.destructive-action), button:checked:hover {
  box-shadow: inset 0 0 0 9999px transparent;
}

actionbar > revealer > box .linked > button:checked:disabled:not(.suggested-action):not(.destructive-action), button:checked:disabled {
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.1);
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.38);
}

.raven-notifications-group list > row button.flat, .raven .expander-button, .budgie-popover button.flat.switcher, window.background > box.vertical > toolbar.primary-toolbar > toolitem > box.horizontal:not(.linked) > button.toggle,
window.background > box.vertical > toolbar.primary-toolbar > toolitem > .linked > button:not(.toggle):not(.raised):not(.flat), window.csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > .linked > button,
window.csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > box.horizontal > button,
window.solid-csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > .linked > button,
window.solid-csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > box.horizontal > button, .nautilus-window headerbar revealer > button, button.titlebutton:not(.suggested-action):not(.destructive-action), filechooser #pathbarbox > stack > box > button, button.close, button.circular, .inline-toolbar button:not(.text-button) {
  border-radius: 9999px;
}

.raven-notifications-group list > row button.flat label, .raven .expander-button label, .budgie-popover button.flat.switcher label, window.background > box.vertical > toolbar.primary-toolbar > toolitem > box.horizontal:not(.linked) > button.toggle label,
window.background > box.vertical > toolbar.primary-toolbar > toolitem > .linked > button:not(.toggle):not(.raised):not(.flat) label, window.csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > .linked > button label,
window.csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > box.horizontal > button label,
window.solid-csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > .linked > button label,
window.solid-csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > box.horizontal > button label, .nautilus-window headerbar revealer > button label, button.titlebutton:not(.suggested-action):not(.destructive-action) label, filechooser #pathbarbox > stack > box > button label, button.close label, button.circular label, .inline-toolbar button:not(.text-button) label {
  padding: 0;
}

.pluma-window paned.horizontal box.vertical box.horizontal button.flat, .gedit-document-panel row button.flat, placessidebar.sidebar row button.sidebar-button, notebook > header tab button.flat, notebook > header tab button.close-button, spinbutton > button {
  min-height: 24px;
  min-width: 24px;
  padding: 0;
  border-radius: 9999px;
}

button {
  min-height: 24px;
  min-width: 24px;
  padding: 5px 5px;
  border-radius: 6px;
  font-weight: 500;
}

button:drop(active) {
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.08);
}

.budgie-session-dialog buttonbox.linked > button,
.budgie-polkit-dialog buttonbox.linked > button,
.budgie-run-dialog buttonbox.linked > button, .budgie-panel button, .budgie-popover row button, .budgie-settings-window buttonbox.inline-toolbar button, #mate-menu button, #MatePanelPopupWindow button, popover.messagepopover .popover-action-area button, tabbox > tab button, placessidebar.sidebar row button.sidebar-button, calendar.button, .budgie-popover scrolledwindow.sidebar:not(.categories) list > row.activatable button.circular, treeview.view header button button.circular, row.activatable button.circular, scrollbar button, notebook > header > tabs > arrow, popover.background modelbutton.flat,
popover.background .menuitem.button.flat, spinbutton > button, .nemo-window .toolbar button, #buttonbox_frame button, .xfce4-panel.background button, .raven stackswitcher.linked > button, .budgie-popover.budgie-menu scrolledwindow.sidebar.categories button.flat.radio.category-button, .lock-dialog button, .mate-panel-menu-bar button, window.background.csd.geary-main-window stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button,
window#GearyMainWindow.background.csd stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button, layouttabbar button, .floating-bar button, filechooser #pathbarbox > stack > box > button, messagedialog .dialog-action-box button, messagedialog .dialog-action-box .linked:not(.vertical) > button, .app-notification button, actionbar > revealer > box button:not(.suggested-action):not(.destructive-action), popover.background.menu button,
popover.background button.model, .nemo-window .primary-toolbar button:not(.text-button), headerbar button:not(.suggested-action):not(.destructive-action), combobox > .linked:not(.vertical) > button:not(:only-child), button.flat {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), border-image 225ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  outline: none;
  box-shadow: inset 0 0 0 9999px transparent;
  background-color: transparent;
  background-image: radial-gradient(circle, transparent 10%, transparent 0%);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1000% 1000%;
  color: rgba(0, 0, 0, 0.6);
}

.budgie-session-dialog buttonbox.linked > button:focus,
.budgie-polkit-dialog buttonbox.linked > button:focus,
.budgie-run-dialog buttonbox.linked > button:focus, .budgie-panel button:focus, .budgie-popover row button:focus, .budgie-settings-window buttonbox.inline-toolbar button:focus, #mate-menu button:focus, #MatePanelPopupWindow button:focus, popover.messagepopover .popover-action-area button:focus, tabbox > tab button:focus, placessidebar.sidebar row button.sidebar-button:focus, calendar.button:focus, .budgie-popover scrolledwindow.sidebar:not(.categories) list > row.activatable button.circular:focus, treeview.view header button button.circular:focus, row.activatable button.circular:focus, scrollbar button:focus, notebook > header > tabs > arrow:focus, popover.background modelbutton.flat:focus,
popover.background .menuitem.button.flat:focus, spinbutton > button:focus, .nemo-window .toolbar button:focus, #buttonbox_frame button:focus, .xfce4-panel.background button:focus, .raven stackswitcher.linked > button:focus, .budgie-popover.budgie-menu scrolledwindow.sidebar.categories button.flat.radio.category-button:focus, .lock-dialog button:focus, .mate-panel-menu-bar button:focus, window.background.csd.geary-main-window stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:focus,
window#GearyMainWindow.background.csd stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:focus, layouttabbar button:focus, .floating-bar button:focus, filechooser #pathbarbox > stack > box > button:focus, messagedialog .dialog-action-box button:focus, messagedialog .dialog-action-box .linked:not(.vertical) > button:focus, .app-notification button:focus, actionbar > revealer > box button:focus:not(.suggested-action):not(.destructive-action), popover.background.menu button:focus,
popover.background button.model:focus, .nemo-window .primary-toolbar button:focus:not(.text-button), headerbar button:focus:not(.suggested-action):not(.destructive-action), combobox > .linked:not(.vertical) > button:focus:not(:only-child), button.flat:focus {
  box-shadow: inset 0 0 0 2px alpha(currentColor, 0.08);
  color: rgba(0, 0, 0, 0.87);
}

.budgie-session-dialog buttonbox.linked > button:hover,
.budgie-polkit-dialog buttonbox.linked > button:hover,
.budgie-run-dialog buttonbox.linked > button:hover, .budgie-panel button:hover, .budgie-popover row button:hover, .budgie-settings-window buttonbox.inline-toolbar button:hover, #mate-menu button:hover, #MatePanelPopupWindow button:hover, popover.messagepopover .popover-action-area button:hover, tabbox > tab button:hover, placessidebar.sidebar row button.sidebar-button:hover, calendar.button:hover, .budgie-popover scrolledwindow.sidebar:not(.categories) list > row.activatable button.circular:hover, treeview.view header button button.circular:hover, row.activatable button.circular:hover, scrollbar button:hover, notebook > header > tabs > arrow:hover, popover.background modelbutton.flat:hover,
popover.background .menuitem.button.flat:hover, spinbutton > button:hover, .nemo-window .toolbar button:hover, #buttonbox_frame button:hover, .xfce4-panel.background button:hover, .raven stackswitcher.linked > button:hover, .budgie-popover.budgie-menu scrolledwindow.sidebar.categories button.flat.radio.category-button:hover, .lock-dialog button:hover, .mate-panel-menu-bar button:hover, window.background.csd.geary-main-window stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:hover,
window#GearyMainWindow.background.csd stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:hover, layouttabbar button:hover, .floating-bar button:hover, filechooser #pathbarbox > stack > box > button:hover, messagedialog .dialog-action-box button:hover, messagedialog .dialog-action-box .linked:not(.vertical) > button:hover, .app-notification button:hover, actionbar > revealer > box button:hover:not(.suggested-action):not(.destructive-action), popover.background.menu button:hover,
popover.background button.model:hover, .nemo-window .primary-toolbar button:hover:not(.text-button), headerbar button:hover:not(.suggested-action):not(.destructive-action), combobox > .linked:not(.vertical) > button:hover:not(:only-child), button.flat:hover {
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.08);
  color: rgba(0, 0, 0, 0.87);
}

.budgie-session-dialog buttonbox.linked > button:active,
.budgie-polkit-dialog buttonbox.linked > button:active,
.budgie-run-dialog buttonbox.linked > button:active, .budgie-panel button:active, .budgie-popover row button:active, .budgie-settings-window buttonbox.inline-toolbar button:active, #mate-menu button:active, #MatePanelPopupWindow button:active, popover.messagepopover .popover-action-area button:active, tabbox > tab button:active, placessidebar.sidebar row button.sidebar-button:active, calendar.button:active, .budgie-popover scrolledwindow.sidebar:not(.categories) list > row.activatable button.circular:active, treeview.view header button button.circular:active, row.activatable button.circular:active, scrollbar button:active, notebook > header > tabs > arrow:active, popover.background modelbutton.flat:active,
popover.background .menuitem.button.flat:active, spinbutton > button:active, .nemo-window .toolbar button:active, #buttonbox_frame button:active, .xfce4-panel.background button:active, .raven stackswitcher.linked > button:active, .budgie-popover.budgie-menu scrolledwindow.sidebar.categories button.flat.radio.category-button:active, .lock-dialog button:active, .mate-panel-menu-bar button:active, window.background.csd.geary-main-window stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:active,
window#GearyMainWindow.background.csd stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:active, layouttabbar button:active, .floating-bar button:active, filechooser #pathbarbox > stack > box > button:active, messagedialog .dialog-action-box button:active, messagedialog .dialog-action-box .linked:not(.vertical) > button:active, .app-notification button:active, actionbar > revealer > box button:active:not(.suggested-action):not(.destructive-action), popover.background.menu button:active,
popover.background button.model:active, .nemo-window .primary-toolbar button:active:not(.text-button), headerbar button:active:not(.suggested-action):not(.destructive-action), combobox > .linked:not(.vertical) > button:active:not(:only-child), button.flat:active {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), border-image 225ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.08);
  background-image: radial-gradient(circle, alpha(currentColor, 0.12) 10%, transparent 0%);
  background-size: 0% 0%;
  color: rgba(0, 0, 0, 0.87);
}

.budgie-session-dialog buttonbox.linked > button:disabled,
.budgie-polkit-dialog buttonbox.linked > button:disabled,
.budgie-run-dialog buttonbox.linked > button:disabled, .budgie-panel button:disabled, .budgie-popover row button:disabled, .budgie-settings-window buttonbox.inline-toolbar button:disabled, #mate-menu button:disabled, #MatePanelPopupWindow button:disabled, popover.messagepopover .popover-action-area button:disabled, tabbox > tab button:disabled, placessidebar.sidebar row button.sidebar-button:disabled, calendar.button:disabled, .budgie-popover scrolledwindow.sidebar:not(.categories) list > row.activatable button.circular:disabled, treeview.view header button button.circular:disabled, row.activatable button.circular:disabled, scrollbar button:disabled, notebook > header > tabs > arrow:disabled, popover.background modelbutton.flat:disabled,
popover.background .menuitem.button.flat:disabled, spinbutton > button:disabled, .nemo-window .toolbar button:disabled, #buttonbox_frame button:disabled, .xfce4-panel.background button:disabled, .raven stackswitcher.linked > button:disabled, .budgie-popover.budgie-menu scrolledwindow.sidebar.categories button.flat.radio.category-button:disabled, .lock-dialog button:disabled, .mate-panel-menu-bar button:disabled, window.background.csd.geary-main-window stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:disabled,
window#GearyMainWindow.background.csd stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:disabled, layouttabbar button:disabled, .floating-bar button:disabled, filechooser #pathbarbox > stack > box > button:disabled, messagedialog .dialog-action-box button:disabled, messagedialog .dialog-action-box .linked:not(.vertical) > button:disabled, .app-notification button:disabled, actionbar > revealer > box button:disabled:not(.suggested-action):not(.destructive-action), popover.background.menu button:disabled,
popover.background button.model:disabled, .nemo-window .primary-toolbar button:disabled:not(.text-button), headerbar button:disabled:not(.suggested-action):not(.destructive-action), combobox > .linked:not(.vertical) > button:disabled:not(:only-child), button.flat:disabled {
  box-shadow: none;
  background-color: transparent;
  color: rgba(0, 0, 0, 0.3);
}

.nemo-window .toolbar button:checked, #buttonbox_frame button:checked, .xfce4-panel.background button:checked, .raven stackswitcher.linked > button:checked, .budgie-popover.budgie-menu scrolledwindow.sidebar.categories button.flat.radio.category-button:checked, .lock-dialog button:checked, .mate-panel-menu-bar button:checked, window.background.csd.geary-main-window stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:checked,
window#GearyMainWindow.background.csd stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:checked, layouttabbar button:checked, .floating-bar button:checked, filechooser #pathbarbox > stack > box > button:checked, messagedialog .dialog-action-box button:checked, messagedialog .dialog-action-box .linked:not(.vertical) > button:checked, .app-notification button:checked, actionbar > revealer > box button:checked:not(.suggested-action):not(.destructive-action), popover.background.menu button:checked,
popover.background button.model:checked, .nemo-window .primary-toolbar button:checked:not(.text-button), headerbar button:checked:not(.suggested-action):not(.destructive-action), combobox > .linked:not(.vertical) > button:checked:not(:only-child), button.flat:checked, button.flat:checked:hover {
  background-color: alpha(currentColor, 0.1);
  color: rgba(0, 0, 0, 0.87);
}

.nemo-window .toolbar button:checked:disabled, #buttonbox_frame button:checked:disabled, .xfce4-panel.background button:checked:disabled, .raven stackswitcher.linked > button:checked:disabled, .budgie-popover.budgie-menu scrolledwindow.sidebar.categories button.flat.radio.category-button:checked:disabled, .lock-dialog button:checked:disabled, .mate-panel-menu-bar button:checked:disabled, window.background.csd.geary-main-window stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:checked:disabled,
window#GearyMainWindow.background.csd stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar button:checked:disabled, layouttabbar button:checked:disabled, .floating-bar button:checked:disabled, filechooser #pathbarbox > stack > box > button:checked:disabled, messagedialog .dialog-action-box button:checked:disabled, .app-notification button:checked:disabled, actionbar > revealer > box button:checked:disabled:not(.suggested-action):not(.destructive-action), popover.background.menu button:checked:disabled,
popover.background button.model:checked:disabled, .nemo-window .primary-toolbar button:checked:disabled:not(.text-button), headerbar button:checked:disabled:not(.suggested-action):not(.destructive-action), combobox > .linked:not(.vertical) > button:checked:disabled:not(:only-child), button.flat:checked:disabled {
  background-color: alpha(currentColor, 0.1);
  color: rgba(0, 0, 0, 0.38);
}

button.text-button {
  min-width: 32px;
  padding-left: 16px;
  padding-right: 16px;
}

button.image-button {
  min-width: 24px;
  padding: 5px;
}

button.text-button.image-button {
  min-width: 24px;
  padding: 5px;
  border-radius: 6px;
}

button.text-button.image-button label:first-child {
  margin-left: 11px;
}

button.text-button.image-button label:last-child {
  margin-right: 11px;
}

button.text-button.image-button.flat label:first-child {
  margin-left: 7px;
}

button.text-button.image-button.flat label:last-child {
  margin-right: 7px;
}

button.text-button.image-button image:not(:only-child) {
  margin: 0 4px;
}

.linked:not(.vertical) > button.flat:not(:only-child), .linked.vertical > button.flat:not(:only-child) {
  border-radius: 6px;
}

button.osd {
  min-width: 24px;
  min-width: 24px;
  padding: 5px;
  background-color: #212121;
  color: #FFFFFF;
}

button.osd:focus {
  box-shadow: none;
}

button.osd:hover {
  background-color: #424242;
  color: #FFFFFF;
}

button.osd:active {
  background-color: #595959;
  color: #FFFFFF;
}

button.osd:disabled {
  opacity: 0;
}

button.osd.image-button, button.osd.circular {
  padding: 11px;
}

button.osd.image-button > image, button.osd.circular > image {
  padding: 0;
}

button.suggested-action {
  background-color: @accent_color;
  color: #FFFFFF;
  box-shadow: none;
}

button.suggested-action:disabled {
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.38);
}

button.suggested-action:hover {
  box-shadow: inset 0 0 0 9999px transparent, 0 2px 2.4px -1px alpha(@accent_color, 0.2), 0 4px 3px 0 alpha(@accent_color, 0.14), 0 1px 6px 0 alpha(@accent_color, 0.12);
}

button.suggested-action:checked {
  background-color: @accent_bg_color;
}

button.suggested-action:checked:hover {
  box-shadow: inset 0 0 0 9999px transparent, 0 3px 3px -3px alpha(@accent_color, 0.3), 0 2px 3px -1px alpha(@accent_color, 0.24), 0 2px 5px 0 alpha(@accent_color, 0.12);
}

button.suggested-action:focus {
  box-shadow: 0 0 0 2px alpha(@accent_color, 0.35);
}

button.suggested-action.flat {
  background-color: transparent;
  color: @accent_color;
}

button.suggested-action.flat:disabled {
  box-shadow: none;
  background-color: transparent;
  color: rgba(0, 0, 0, 0.3);
}

button.suggested-action.flat:checked {
  background-color: alpha(@accent_color, 0.3);
}

button.destructive-action {
  background-color: #E53935;
  color: #FFFFFF;
  box-shadow: none;
}

button.destructive-action:disabled {
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.38);
}

button.destructive-action:hover {
  box-shadow: inset 0 0 0 9999px transparent, 0 2px 2.4px -1px rgba(229, 57, 53, 0.2), 0 4px 3px 0 rgba(229, 57, 53, 0.14), 0 1px 6px 0 rgba(229, 57, 53, 0.12);
}

button.destructive-action:checked {
  background-color: #ed7472;
}

button.destructive-action:checked:hover {
  box-shadow: inset 0 0 0 9999px transparent, 0 3px 3px -3px rgba(229, 57, 53, 0.3), 0 2px 3px -1px rgba(229, 57, 53, 0.24), 0 2px 5px 0 rgba(229, 57, 53, 0.12);
}

button.destructive-action:focus {
  box-shadow: 0 0 0 2px rgba(229, 57, 53, 0.35);
}

button.destructive-action.flat {
  background-color: transparent;
  color: #E53935;
}

button.destructive-action.flat:disabled {
  box-shadow: none;
  background-color: transparent;
  color: rgba(0, 0, 0, 0.3);
}

button.destructive-action.flat:checked {
  background-color: rgba(229, 57, 53, 0.3);
}

.stack-switcher > button > label {
  margin: 0 -6px;
  padding: 0 6px;
}

.stack-switcher > button > image {
  margin: -3px -6px;
  padding: 3px 6px;
}

.stack-switcher > button.needs-attention:checked > label,
.stack-switcher > button.needs-attention:checked > image {
  animation: none;
  background-image: none;
}

.primary-toolbar button {
  -gtk-icon-shadow: none;
}

button.close, button.circular {
  min-width: 24px;
  padding: 5px;
}

stacksidebar.sidebar row.needs-attention > label, .stack-switcher > button.needs-attention > label,
.stack-switcher > button.needs-attention > image {
  animation: needs-attention 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-repeat: no-repeat;
  background-position: right 3px;
  background-size: 6px 6px;
}

stacksidebar.sidebar row.needs-attention > label:dir(rtl), .stack-switcher > button.needs-attention > label:dir(rtl),
.stack-switcher > button.needs-attention > image:dir(rtl) {
  background-position: left 3px;
}

button.color {
  min-height: 24px;
  min-width: 24px;
  padding: 6px;
}

/*********
 * Links *
 *********/
*:link, link {
  color: #5bd3f8;
}

*:visited {
  color: #AB47BC;
}

button.link:link, button.link:link:focus, button.link:link:hover, button.link:link:active {
  color: #5bd3f8;
}

button.link:visited, button.link:visited:focus, button.link:visited:hover, button.link:visited:active {
  color: #AB47BC;
}

button.link > label {
  text-decoration-line: underline;
}

/*****************
 * GtkSpinButton *
 *****************/
spinbutton {
  padding: 0;
}

spinbutton > entry, .background:not(.csd) spinbutton > entry {
  min-width: 30px;
  margin: 0;
  border-radius: 0;
  border-image: none;
}

spinbutton > entry, spinbutton > entry:focus, spinbutton > entry:disabled, .background:not(.csd) spinbutton > entry, .background:not(.csd) spinbutton > entry:focus, .background:not(.csd) spinbutton > entry:disabled {
  border: none;
  box-shadow: none;
  background-color: transparent;
}

spinbutton > button {
  border: solid 6px transparent;
}

spinbutton > button:focus:not(:hover):not(:active):not(:disabled) {
  box-shadow: inset 0 0 0 9999px transparent;
  color: rgba(0, 0, 0, 0.6);
}

spinbutton > button.up:dir(ltr), spinbutton > button.down:dir(rtl) {
  margin-left: -3px;
}

spinbutton > button.up:dir(rtl), spinbutton > button.down:dir(ltr) {
  margin-right: -3px;
}

spinbutton.vertical {
  padding: 3px;
}

spinbutton.vertical > entry {
  margin: 0;
  padding: 0;
  min-height: 34px;
  min-width: 0;
}

spinbutton.vertical > button.up {
  margin: 0;
}

spinbutton.vertical > button.down {
  margin: 0;
}

treeview spinbutton:not(.vertical) {
  min-height: 0;
  border-style: none;
  border-radius: 0;
}

treeview spinbutton:not(.vertical) entry {
  min-height: 0;
  padding: 1px 2px;
}

/**************
 * ComboBoxes *
 **************/
combobox arrow {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
  min-height: 16px;
  min-width: 16px;
}

combobox decoration {
  transition: none;
}

combobox button.combo cellview:dir(ltr) {
  margin-left: -1px;
}

combobox button.combo cellview:dir(rtl) {
  margin-right: -1px;
}

combobox.linked button:nth-child(2):dir(ltr) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

combobox.linked button:nth-child(2):dir(rtl) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

combobox > .linked:not(.vertical) > entry:not(:only-child) {
  border-radius: 6px;
}

combobox > .linked:not(.vertical) > entry:not(:only-child):first-child {
  margin-right: -34px;
  padding-right: 34px;
}

combobox > .linked:not(.vertical) > entry:not(:only-child):last-child {
  margin-left: -34px;
  padding-left: 34px;
}

combobox > .linked:not(.vertical) > button:not(:only-child) {
  min-height: 16px;
  min-width: 16px;
  margin: 5px;
  padding: 4px;
  border-radius: 6px;
}

combobox > .linked > button.combo {
  padding: 5px 9px;
}

.linked:not(.vertical) > combobox:not(:first-child) > box > button.combo {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.linked:not(.vertical) > combobox:not(:last-child) > box > button.combo {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.linked.vertical > combobox:not(:first-child) > box > button.combo {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.linked.vertical > combobox:not(:last-child) > box > button.combo {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

button.combo:only-child {
  border-radius: 6px;
  font-weight: normal;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 300ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.87);
}

button.combo:only-child:focus {
  background-color: alpha(currentColor, 0.08);
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.26);
}

button.combo:only-child:hover {
  background-color: alpha(currentColor, 0.08);
  box-shadow: inset 0 0 0 2px alpha(currentColor, 0.08);
}

button.combo:only-child:checked {
  background-color: rgba(0, 0, 0, 0.04);
  box-shadow: inset 0 0 0 2px @accent_color;
}

button.combo:only-child:disabled {
  box-shadow: inset 0 0 0 2px transparent;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.38);
}

/************
 * Toolbars *
 ************/
toolbar {
  -GtkWidget-window-dragging: true;
  padding: 2px 3px;
  background-color: #FFFFFF;
}

.osd toolbar {
  background-color: transparent;
}

frame.documents-dropdown, .app-notification, toolbar.osd {
  transition: box-shadow 200ms ease-out;
  padding: 6px;
  border-radius: 12px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15), 0 3px 3px 0 rgba(0, 0, 0, 0.18), 0 3px 6px 0 rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.15);
  background-color: #FFFFFF;
}

frame.documents-dropdown:backdrop, .app-notification:backdrop, toolbar.osd:backdrop {
  box-shadow: 0 4px 3px -3px rgba(0, 0, 0, 0.2), 0 2px 2px -1px rgba(0, 0, 0, 0.24), 0 1px 3px 0 rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.15);
}

frame.left.documents-dropdown, .left.app-notification, frame.right.documents-dropdown, .right.app-notification, frame.top.documents-dropdown, .top.app-notification, frame.bottom.documents-dropdown, .bottom.app-notification, toolbar.osd.left, toolbar.osd.right, toolbar.osd.top, toolbar.osd.bottom {
  border-radius: 0;
}

frame.bottom.documents-dropdown, .bottom.app-notification, toolbar.osd.bottom {
  box-shadow: none;
  background-color: transparent;
  background-image: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.1) 30%, rgba(0, 0, 0, 0.2) 50%, rgba(0, 0, 0, 0.4));
}

toolbar.horizontal > separator {
  margin: 2px;
}

toolbar.vertical > separator {
  margin: 2px;
}

toolbar:not(.inline-toolbar):not(.osd) scale,
toolbar:not(.inline-toolbar):not(.osd) entry,
toolbar:not(.inline-toolbar):not(.osd) spinbutton,
toolbar:not(.inline-toolbar):not(.osd) button {
  margin: 2px 1px;
}

toolbar:not(.inline-toolbar):not(.osd) .linked entry:not(:first-child),
toolbar:not(.inline-toolbar):not(.osd) .linked spinbutton:not(:first-child),
toolbar:not(.inline-toolbar):not(.osd) .linked button:not(:first-child) {
  margin-left: 0;
}

toolbar:not(.inline-toolbar):not(.osd) .linked entry:not(:last-child),
toolbar:not(.inline-toolbar):not(.osd) .linked spinbutton:not(:last-child),
toolbar:not(.inline-toolbar):not(.osd) .linked button:not(:last-child) {
  margin-right: 0;
}

toolbar:not(.inline-toolbar):not(.osd) spinbutton entry,
toolbar:not(.inline-toolbar):not(.osd) spinbutton button {
  margin: 0;
}

toolbar:not(.inline-toolbar):not(.osd) switch {
  margin: 8px 2px;
}

.toolbar {
  background-color: #FAFAFA;
}

frame .toolbar {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.inline-toolbar {
  padding: 6px;
  border-style: solid;
  border-width: 0 1px 1px;
  border-color: rgba(0, 0, 0, 0.12);
  background-color: #FAFAFA;
}

.frame .inline-toolbar {
  border-width: 1px 0 0;
  background-color: transparent;
}

searchbar > revealer > box,
.location-bar {
  padding: 6px;
  border-style: solid;
  border-width: 0 0 1px;
  border-color: rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
  background-clip: border-box;
}

searchbar > revealer > box {
  margin: -6px;
}

/***************
 * Header bars *
 ***************/
.nemo-window .primary-toolbar button:not(.text-button), headerbar button:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.7);
}

.nemo-window .primary-toolbar .linked:not(.vertical) > button:not(.text-button), headerbar .linked:not(.vertical) > button:not(.suggested-action):not(.destructive-action) {
  border-radius: 6px;
}

.nemo-window .primary-toolbar button:focus:not(.text-button), headerbar button:focus:not(.suggested-action):not(.destructive-action), .nemo-window .primary-toolbar button:hover:not(.text-button), headerbar button:hover:not(.suggested-action):not(.destructive-action), .nemo-window .primary-toolbar button:active:not(.text-button), headerbar button:active:not(.suggested-action):not(.destructive-action), .nemo-window .primary-toolbar button:checked:not(.text-button), headerbar button:checked:not(.suggested-action):not(.destructive-action) {
  color: #FFFFFF;
}

.nemo-window .primary-toolbar button:disabled:not(.text-button), headerbar button:disabled:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.32);
}

.nemo-window .primary-toolbar button:checked:disabled:not(.text-button), headerbar button:checked:disabled:not(.suggested-action):not(.destructive-action) {
  background-color: transparent;
  color: rgba(255, 255, 255, 0.5);
}

.nemo-window .primary-toolbar button:backdrop:not(.text-button), headerbar button:backdrop:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.5);
}

.nemo-window .primary-toolbar button:backdrop:focus:not(.text-button), headerbar button:backdrop:focus:not(.suggested-action):not(.destructive-action), .nemo-window .primary-toolbar button:backdrop:hover:not(.text-button), headerbar button:backdrop:hover:not(.suggested-action):not(.destructive-action), .nemo-window .primary-toolbar button:backdrop:active:not(.text-button), headerbar button:backdrop:active:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.7);
}

.nemo-window .primary-toolbar button:backdrop:disabled:not(.text-button), headerbar button:backdrop:disabled:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.32);
}

.nemo-window .primary-toolbar button:backdrop:checked:not(.text-button), headerbar button:backdrop:checked:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.7);
}

.nemo-window .primary-toolbar button:backdrop:checked:disabled:not(.text-button), headerbar button:backdrop:checked:disabled:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.32);
}

.nemo-window .primary-toolbar entry, .titlebar entry {
  background-color: rgba(255, 255, 255, 0.04);
  color: #FFFFFF;
}

.nemo-window .primary-toolbar entry:disabled, .titlebar entry:disabled {
  background-color: rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.5);
}

.nemo-window .primary-toolbar entry image, .titlebar entry image {
  color: rgba(255, 255, 255, 0.7);
}

.nemo-window .primary-toolbar entry image:hover, .titlebar entry image:hover, .nemo-window .primary-toolbar entry image:active, .titlebar entry image:active {
  color: #FFFFFF;
}

.nemo-window .primary-toolbar entry image:disabled, .titlebar entry image:disabled {
  color: rgba(255, 255, 255, 0.5);
}

.titlebar {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  background-color: @headerbar_bg_color;
  color: @headerbar_fg_color;
  border-radius: 12px 12px 0 0;
  box-shadow: inset 0 -1px rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.1);
}

.titlebar:disabled {
  color: rgba(255, 255, 255, 0.5);
}

.titlebar:backdrop {
  color: rgba(255, 255, 255, 0.7);
}

.titlebar:backdrop:disabled {
  color: rgba(255, 255, 255, 0.32);
}

.csd .titlebar:backdrop {
  background-color: #2C2C2C;
}

.titlebar .title {
  padding: 0 12px;
  font-weight: bold;
}

.titlebar .subtitle {
  padding: 0 12px;
  font-size: smaller;
}

.titlebar .subtitle,
.titlebar .dim-label {
  transition: color 75ms cubic-bezier(0, 0, 0.2, 1);
  color: rgba(255, 255, 255, 0.7);
}

.titlebar .subtitle:backdrop,
.titlebar .dim-label:backdrop {
  color: rgba(255, 255, 255, 0.5);
}

.titlebar .titlebar {
  background-color: transparent;
  box-shadow: none;
}

.titlebar + separator, .titlebar + separator.sidebar {
  background-color: #242424;
  background-image: none;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 -1px rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.1);
}

.titlebar + separator:backdrop, .titlebar + separator.sidebar:backdrop {
  background-color: #2C2C2C;
}

.titlebar.selection-mode + separator, .titlebar.selection-mode + separator.sidebar, .selection-mode .titlebar + separator, .selection-mode .titlebar + separator.sidebar {
  background-color: @accent_color;
}

.titlebar.selection-mode + separator:backdrop, .titlebar.selection-mode + separator.sidebar:backdrop, .selection-mode .titlebar + separator:backdrop, .selection-mode .titlebar + separator.sidebar:backdrop {
  background-color: @accent_color;
}

.background.csd.unified .titlebar + separator, .background.csd.unified .titlebar + separator.sidebar {
  box-shadow: inset 0 -1px rgba(0, 0, 0, 0.12);
}

.titlebar .linked:not(.vertical) > entry {
  border-radius: 6px;
  margin-left: 3px;
  margin-right: 3px;
}

.titlebar button.suggested-action:disabled, .titlebar button.destructive-action:disabled {
  background-color: rgba(255, 255, 255, 0.04);
  color: rgba(255, 255, 255, 0.5);
}

.titlebar stackswitcher {
  background-color: rgba(255, 255, 255, 0.04);
}

.titlebar stackswitcher.linked:not(.vertical) > button:not(.suggested-action):not(.destructive-action):checked {
  background-color: rgba(255, 255, 255, 0.15);
  color: #FFFFFF;
}

.titlebar .path-bar button:not(.suggested-action):not(.destructive-action).text-button {
  min-width: 0;
  padding-left: 5px;
  padding-right: 5px;
}

.titlebar.selection-mode {
  transition: background-color 0.1ms 225ms, color 75ms cubic-bezier(0, 0, 0.2, 1);
  animation: ripple-on-headerbar 225ms cubic-bezier(0, 0, 0.2, 1);
  background-color: @accent_color;
  color: #FFFFFF;
  box-shadow: inset 0 -1px rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.2);
}

.titlebar.selection-mode:backdrop {
  color: rgba(255, 255, 255, 0.7);
  background-color: @accent_bg_color;
}

.titlebar.selection-mode .subtitle:link {
  color: #FFFFFF;
}

.titlebar.selection-mode button:not(.suggested-action):not(.destructive-action) {
  color: #FFFFFF;
}

.titlebar.selection-mode button:not(.suggested-action):not(.destructive-action):disabled {
  color: rgba(255, 255, 255, 0.5);
}

.titlebar.selection-mode button:not(.suggested-action):not(.destructive-action):checked {
  color: #FFFFFF;
}

.titlebar.selection-mode button:not(.suggested-action):not(.destructive-action):checked:disabled {
  color: rgba(255, 255, 255, 0.5);
}

.titlebar.selection-mode button:not(.suggested-action):not(.destructive-action):backdrop:not(.titlebutton) {
  color: rgba(255, 255, 255, 0.7);
}

.titlebar.selection-mode button:not(.suggested-action):not(.destructive-action):backdrop:disabled {
  color: rgba(255, 255, 255, 0.32);
}

.titlebar.selection-mode button:not(.suggested-action):not(.destructive-action):backdrop:checked {
  color: rgba(255, 255, 255, 0.7);
}

.titlebar.selection-mode button:not(.suggested-action):not(.destructive-action):backdrop:checked:disabled {
  color: rgba(255, 255, 255, 0.32);
}

.titlebar.selection-mode .selection-menu {
  padding-left: 16px;
  padding-right: 16px;
}

.titlebar.selection-mode .selection-menu arrow {
  -GtkArrow-arrow-scaling: 1;
}

.titlebar.selection-mode .selection-menu .arrow {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

.tiled .titlebar, .tiled-top .titlebar, .tiled-right .titlebar, .tiled-bottom .titlebar, .tiled-left .titlebar, .maximized .titlebar, .fullscreen .titlebar {
  border-radius: 0;
}

.titlebar.default-decoration {
  min-height: 24px;
  padding: 6px 12px;
  border-radius: 12px 12px 0 0;
  border: none;
  background-color: #242424;
  background-image: none;
  box-shadow: inset 0 1px rgba(255, 255, 255, 0.1);
}

.titlebar.default-decoration:backdrop {
  background-color: #2C2C2C;
}

.tiled .titlebar.default-decoration, .maximized .titlebar.default-decoration, .fullscreen .titlebar.default-decoration {
  box-shadow: none;
  border-radius: 0;
}

.titlebar.default-decoration button.titlebutton {
  min-height: 24px;
  min-width: 24px;
  margin: 0;
  padding: 0;
}

.background.csd .titlebar.default-decoration {
  padding: 6px;
}

.background:not(.csd) .titlebar.default-decoration button.titlebutton:active {
  background-size: 1000% 1000%;
}

.solid-csd .titlebar:dir(rtl), .solid-csd .titlebar:dir(ltr) {
  border-radius: 0;
  box-shadow: none;
}

headerbar {
  min-height: 46px;
  padding: 0 6px;
}

box.vertical headerbar {
  background-color: #242424;
}

headerbar entry,
headerbar spinbutton,
headerbar button,
headerbar stackswitcher {
  margin-top: 6px;
  margin-bottom: 6px;
}

headerbar button, headerbar button.image-button {
  border-radius: 6px;
}

headerbar > box.left,
headerbar > box.right {
  padding: 0 4px;
}

headerbar separator.titlebutton, headerbar separator.sidebar {
  margin-top: 11.5px;
  margin-bottom: 11.5px;
  background-color: transparent;
}

headerbar switch {
  margin-top: 11px;
  margin-bottom: 11px;
}

headerbar spinbutton button {
  margin-top: 0;
  margin-bottom: 0;
}

headerbar .entry-tag, headerbar .photos-entry-tag, headerbar .documents-entry-tag {
  margin-top: 5px;
  margin-bottom: 5px;
}

headerbar.windowhandle viewswitcher button:not(.titlebutton):not(.suggested-action):not(.destructive-action) {
  border-radius: 0;
  margin: 0;
  min-width: 120px;
  padding: 0;
}

headerbar.windowhandle viewswitcher button:not(.titlebutton):not(.suggested-action):not(.destructive-action) > stack > box {
  padding: 0 12px;
}

headerbar.windowhandle viewswitcher button:not(.titlebutton):not(.suggested-action):not(.destructive-action):focus {
  box-shadow: none;
}

headerbar.windowhandle > button.popup label, headerbar.windowhandle > button.popup image {
  min-height: 0;
}

headerbar.windowhandle viewswitchertitle > squeezer {
  margin-top: 0;
  margin-bottom: 0;
  background: none;
}

headerbar.windowhandle viewswitchertitle > squeezer > viewswitcher {
  margin: 0 0;
  background: none;
}

headerbar.windowhandle viewswitchertitle > squeezer > viewswitcher > box.horizontal > button.radio {
  margin: 0;
  padding: 0;
  border-radius: 0;
}

/************
 * Pathbars *
 ************/
.caja-pathbar button,
.path-bar.linked:not(.vertical) > button {
  padding-left: 5px;
  padding-right: 5px;
  border-radius: 3px;
  margin-left: 1px;
  margin-right: 1px;
  background-color: alpha(currentColor, 0.08);
}

.caja-pathbar button:disabled,
.path-bar.linked:not(.vertical) > button:disabled {
  background-color: alpha(currentColor, 0.05);
}

.caja-pathbar button:first-child,
.path-bar.linked:not(.vertical) > button:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.caja-pathbar button:last-child,
.path-bar.linked:not(.vertical) > button:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.caja-pathbar button:checked,
.path-bar.linked:not(.vertical) > button:checked {
  background-color: alpha(currentColor, 0.16);
  color: rgba(0, 0, 0, 0.87);
}

.caja-pathbar button label, .caja-pathbar button image,
.path-bar.linked:not(.vertical) > button label,
.path-bar.linked:not(.vertical) > button image {
  margin-left: 3px;
  margin-right: 3px;
}

.caja-pathbar button.slider-button,
.path-bar.linked:not(.vertical) > button.slider-button {
  padding-left: 4px;
  padding-right: 4px;
}

/**************
 * Tree Views *
 **************/
treeview.view {
  border-left-color: rgba(0, 0, 0, 0.26);
  border-top-color: rgba(0, 0, 0, 0.12);
}

* {
  -GtkTreeView-horizontal-separator: 4;
  -GtkTreeView-grid-line-width: 1;
  -GtkTreeView-grid-line-pattern: '';
  -GtkTreeView-tree-line-width: 1;
  -GtkTreeView-tree-line-pattern: '';
  -GtkTreeView-expander-size: 16;
}

.csd treeview.view:not(:selected):not(:hover):not(.progressbar):not(.expander):not(.trough):not(.separator) {
  background-color: transparent;
}

treeview.view:selected {
  background-color: rgba(224, 224, 224, 0.9805);
  color: rgba(0, 0, 0, 0.87);
}

treeview.view.separator {
  min-height: 6px;
  color: rgba(0, 0, 0, 0.12);
}

treeview.view:drop(active) {
  border-style: solid none;
  border-width: 9999px;
  border-color: alpha(currentColor, 0.08);
}

treeview.view:drop(active).after {
  border-top-style: none;
}

treeview.view:drop(active).before {
  border-bottom-style: none;
}

treeview.view.expander {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
  -gtk-icon-transform: rotate(-90deg);
  color: rgba(0, 0, 0, 0.6);
}

treeview.view.expander:dir(rtl) {
  -gtk-icon-transform: rotate(90deg);
}

treeview.view.expander:checked {
  -gtk-icon-transform: unset;
}

treeview.view.expander:hover, treeview.view.expander:active {
  color: rgba(0, 0, 0, 0.87);
}

treeview.view.expander:disabled {
  color: rgba(0, 0, 0, 0.3);
}

treeview.view.progressbar {
  border: none;
  box-shadow: none;
  background-color: @accent_color;
  background-image: none;
  border-radius: 9999px;
  color: #FFFFFF;
}

treeview.view.progressbar:selected, treeview.view.progressbar:selected:hover, treeview.view.progressbar:selected:focus {
  box-shadow: none;
  background-color: alpha(@accent_color, 0.9805);
  color: #FFFFFF;
}

treeview.view.progressbar:selected:backdrop, treeview.view.progressbar:selected:hover:backdrop, treeview.view.progressbar:selected:focus:backdrop {
  color: #FFFFFF;
}

treeview.view.progressbar:backdrop, treeview.view.progressbar:selected:backdrop {
  background-color: rgba(0, 0, 0, 0.26);
}

treeview.view.trough {
  border: none;
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0.12);
  background-image: none;
  border-radius: 9999px;
  padding: 0;
  margin: 0;
}

treeview.view.trough:selected, treeview.view.trough:selected:hover, treeview.view.trough:selected:focus {
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0.12);
}

treeview.view.trough:backdrop, treeview.view.trough:selected:backdrop {
  background-color: rgba(0, 0, 0, 0.12);
}

treeview.view header button {
  padding: 2px 6px;
  border-style: none solid solid none;
  border-width: 1px;
  border-color: rgba(0, 0, 0, 0.12);
  border-radius: 0;
  background-clip: border-box;
}

treeview.view header button:not(:focus):not(:hover):not(:active) {
  color: rgba(0, 0, 0, 0.6);
}

treeview.view header button, treeview.view header button:disabled {
  background-color: #FFFFFF;
}

treeview.view header button:last-child {
  border-right-style: none;
}

treeview.view button.dnd,
treeview.view header.button.dnd {
  padding: 2px 6px;
  border-style: none solid solid;
  border-width: 1px;
  border-color: rgba(0, 0, 0, 0.12);
  border-radius: 0;
  box-shadow: none;
  background-color: #FFFFFF;
  background-clip: border-box;
  color: @accent_color;
}

treeview.view acceleditor > label {
  background-color: @accent_color;
}

/*********
 * Menus *
 *********/
menubar,
.menubar {
  -GtkWidget-window-dragging: true;
  padding: 0;
  background-color: #242424;
  color: #FFFFFF;
  box-shadow: inset 0 -1px rgba(0, 0, 0, 0.12);
}

menubar:backdrop,
.menubar:backdrop {
  color: rgba(255, 255, 255, 0.7);
  background-color: #2C2C2C;
}

.csd menubar, .csd .menubar {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
}

menubar > menuitem,
.menubar > menuitem {
  transition: none;
  min-height: 20px;
  padding: 4px 8px;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
}

menubar > menuitem:hover,
.menubar > menuitem:hover {
  transition: none;
  background-color: alpha(currentColor, 0.1);
  color: #FFFFFF;
}

menubar > menuitem:backdrop,
.menubar > menuitem:backdrop {
  color: rgba(255, 255, 255, 0.5);
}

menubar > menuitem:disabled,
.menubar > menuitem:disabled {
  color: rgba(255, 255, 255, 0.32);
}

menubar > menuitem label:disabled,
.menubar > menuitem label:disabled {
  color: inherit;
}

menubar > menuitem > window.popup.background > menu menuitem,
.menubar > menuitem > window.popup.background > menu menuitem {
  transition: none;
}

.background.popup {
  background-color: transparent;
}

menu {
  margin: 6px;
  padding: 6px;
  background-color: @popover_bg_color;
  background-clip: border-box;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.csd menu {
  border: none;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15);
}

menu menuitem {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  min-height: 20px;
  min-width: 40px;
  padding: 4px 8px;
  color: rgba(0, 0, 0, 0.87);
  font: initial;
  text-shadow: none;
  border-radius: 6px;
}

menu menuitem:hover {
  background-color: alpha(currentColor, 0.08);
}

menu menuitem:active {
  background-color: alpha(currentColor, 0.12);
}

menu menuitem:disabled {
  color: rgba(0, 0, 0, 0.38);
}

menu menuitem accelerator {
  color: rgba(0, 0, 0, 0.6);
}

menu menuitem:disabled accelerator {
  color: rgba(0, 0, 0, 0.3);
}

menu menuitem arrow {
  min-height: 16px;
  min-width: 16px;
}

menu menuitem arrow:dir(ltr) {
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic");
  margin-left: 8px;
}

menu menuitem arrow:dir(rtl) {
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic-rtl");
  margin-right: 8px;
}

menu menuitem label:dir(rtl), menu menuitem label:dir(ltr) {
  color: inherit;
}

menu .view:selected {
  background-color: rgba(235, 235, 235, 0.987);
}

menu > arrow {
  min-height: 16px;
  min-width: 16px;
  padding: 4px;
  background-color: #FFFFFF;
  color: rgba(0, 0, 0, 0.6);
}

menu > arrow.top {
  margin-top: 0;
  border-radius: 6px;
  -gtk-icon-source: -gtk-icontheme("pan-up-symbolic");
}

menu > arrow.bottom {
  margin-top: 8px;
  margin-bottom: -12px;
  border-radius: 6px;
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

menu > arrow:hover {
  background-image: image(alpha(currentColor, 0.08));
  color: rgba(0, 0, 0, 0.87);
}

menu > arrow:disabled {
  border-color: transparent;
  background-color: transparent;
  color: transparent;
}

menu separator {
  margin: 4px 0;
}

/************
 * Popovers *
 ************/
popover.background {
  transition: box-shadow 200ms ease-out;
  padding: 0;
  background-color: @popover_bg_color;
  border-radius: 12px;
}

popover.background, .csd popover.background {
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.05), 0 4px 6px 0 rgba(0, 0, 0, 0.06), 0 1px 10px 0 rgba(0, 0, 0, 0.05);
  background-clip: padding-box;
}

popover.background:backdrop, .csd popover.background:backdrop {
  box-shadow: 0 3px 3px -2px rgba(0, 0, 0, 0.05), 0 2px 3px -1px rgba(0, 0, 0, 0.06), 0 1px 4px 0 rgba(0, 0, 0, 0.05);
}

popover.background > stack {
  margin: -4px;
}

popover.background > toolbar {
  margin: 0;
}

popover.background > list,
popover.background > .view,
popover.background > toolbar {
  border-style: none;
  box-shadow: none;
  background-color: transparent;
}

popover.background > scrolledwindow > viewport.frame > list {
  background-color: transparent;
  padding: 6px;
}

popover.background > scrolledwindow > viewport.frame > list > row {
  border-radius: 6px;
  padding: 6px;
}

popover.background .view:not(:selected),
popover.background toolbar {
  background-color: #FFFFFF;
}

popover.background .linked > button:not(.radio) {
  border-radius: 6px;
}

popover.background .linked > button:not(.radio):first-child {
  border-radius: 6px;
}

popover.background .linked > button:not(.radio):last-child {
  border-radius: 6px;
}

popover.background .linked > button:not(.radio):only-child {
  border-radius: 6px;
}

popover.background.menu button,
popover.background button.model {
  min-height: 32px;
  padding: 0 8px;
  border-radius: 6px;
}

popover.background modelbutton.flat,
popover.background .menuitem.button.flat {
  min-height: 28px;
  padding: 0 8px;
  border-radius: 6px;
  color: rgba(0, 0, 0, 0.87);
}

popover.background modelbutton.flat + modelbutton.flat,
popover.background modelbutton.flat + box.vertical,
popover.background .menuitem.button.flat + .menuitem.button.flat {
  margin-top: 2px;
}

popover.background modelbutton.flat arrow.left {
  -gtk-icon-source: -gtk-icontheme("pan-start-symbolic");
}

popover.background modelbutton.flat arrow.right {
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic");
}

popover.background separator {
  margin: 3px 0;
}

popover.background list separator {
  margin: 0;
}

/*************
 * Notebooks *
 *************/
tabbox > tab, notebook > header tab {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  min-height: 24px;
  min-width: 24px;
  padding: 3px 12px;
  border: none;
  outline: none;
  background-clip: padding-box;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 500;
  border-radius: 6px;
}

tabbox > tab:hover, notebook > header tab:hover {
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.87);
}

tabbox > tab:disabled, notebook > header tab:disabled {
  color: rgba(0, 0, 0, 0.3);
}

tabbox > tab:checked, notebook > header tab:checked {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  background-color: white;
  color: rgba(0, 0, 0, 0.87);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

tabbox > tab:checked:disabled, notebook > header tab:checked:disabled {
  color: rgba(0, 0, 0, 0.38);
}

notebook {
  background-color: #FFFFFF;
}

frame > paned > notebook > header, notebook.frame > header {
  background-color: rgba(0, 0, 0, 0.04);
}

notebook.frame {
  border-radius: 12px;
}

notebook.frame scrolledwindow.frame {
  border: none;
}

notebook.frame frame > border {
  border: none;
  border-radius: 6px;
}

notebook.frame frame > list row.activatable {
  border-radius: 6px;
}

notebook > header {
  border: none;
  background-color: rgba(0, 0, 0, 0.04);
  padding: 3px;
  margin: 3px;
  border-radius: 9px;
}

notebook > header.top > tabs > arrow {
  border-top-style: none;
}

notebook > header.bottom > tabs > arrow {
  border-bottom-style: none;
}

notebook > header.top > tabs > arrow, notebook > header.bottom > tabs > arrow {
  padding-left: 4px;
  padding-right: 4px;
}

notebook > header.top > tabs > arrow.down, notebook > header.bottom > tabs > arrow.down {
  margin-left: 0;
  -gtk-icon-source: -gtk-icontheme("pan-start-symbolic");
}

notebook > header.top > tabs > arrow.up, notebook > header.bottom > tabs > arrow.up {
  margin-right: 0;
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic");
}

notebook > header.left > tabs > arrow {
  border-left-style: none;
}

notebook > header.right > tabs > arrow {
  border-right-style: none;
}

notebook > header.left > tabs > arrow, notebook > header.right > tabs > arrow {
  padding-top: 4px;
  padding-bottom: 4px;
}

notebook > header.left > tabs > arrow.down, notebook > header.right > tabs > arrow.down {
  margin-top: 0;
  -gtk-icon-source: -gtk-icontheme("pan-up-symbolic");
}

notebook > header.left > tabs > arrow.up, notebook > header.right > tabs > arrow.up {
  margin-bottom: 0;
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

notebook > header > tabs > arrow {
  min-height: 16px;
  min-width: 16px;
  border-radius: 6px;
}

notebook > header tab > box {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  margin: -6px -12px;
  padding: 6px 12px;
}

notebook > header tab > box:drop(active) {
  background-color: rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.87);
}

notebook > header tab button.flat:last-child, notebook > header tab button.close-button:last-child {
  margin-left: 6px;
  margin-right: -6px;
}

notebook > header tab button.flat:first-child, notebook > header tab button.close-button:first-child {
  margin-left: -6px;
  margin-right: 6px;
}

notebook > header.top tabs > tab + tab, notebook > header.bottom tabs > tab + tab {
  margin-left: 3px;
}

notebook > header.top tabs:not(:only-child):first-child, notebook > header.bottom tabs:not(:only-child):first-child {
  margin-left: 0;
}

notebook > header.top tabs:not(:only-child):last-child, notebook > header.bottom tabs:not(:only-child):last-child {
  margin-right: 0;
}

notebook > header.top tabs tab.reorderable-page, notebook > header.bottom tabs tab.reorderable-page {
  border-style: solid;
}

notebook > header.left tabs > tab + tab, notebook > header.right tabs > tab + tab {
  margin-top: 3px;
}

notebook > header.left tabs:not(:only-child):first-child, notebook > header.right tabs:not(:only-child):first-child {
  margin-top: 0;
}

notebook > header.left tabs:not(:only-child):last-child, notebook > header.right tabs:not(:only-child):last-child {
  margin-bottom: 0;
}

notebook > header.left tabs tab.reorderable-page, notebook > header.right tabs tab.reorderable-page {
  border-style: solid;
}

notebook > header > button.image-button {
  min-height: 24px;
  min-width: 24px;
  padding: 3px;
}

notebook > stack:not(:only-child) {
  background-color: transparent;
  border-radius: 6px;
}

/**************
 * Scrollbars *
 **************/
scrollbar {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  background-color: #FFFFFF;
}

* {
  -GtkScrollbar-has-backward-stepper: false;
  -GtkScrollbar-has-forward-stepper: false;
}

scrollbar.top {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

scrollbar.bottom {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

scrollbar.left {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

scrollbar.right {
  border-left: 1px solid rgba(0, 0, 0, 0.12);
}

scrollbar slider {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  min-width: 8px;
  min-height: 8px;
  border: 4px solid transparent;
  border-radius: 9999px;
  background-clip: padding-box;
  background-color: rgba(0, 0, 0, 0.38);
}

scrollbar slider:hover {
  background-color: rgba(0, 0, 0, 0.6);
}

scrollbar slider:active {
  background-color: rgba(0, 0, 0, 0.87);
}

scrollbar slider:disabled {
  background-color: rgba(0, 0, 0, 0.3);
}

scrollbar.fine-tune slider {
  min-width: 4px;
  min-height: 4px;
}

scrollbar.fine-tune.horizontal slider {
  margin: 2px 0;
}

scrollbar.fine-tune.vertical slider {
  margin: 0 2px;
}

scrollbar.overlay-indicator:not(.fine-tune) slider {
  transition-property: background-color, min-height, min-width;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering) {
  border-color: transparent;
  background-color: transparent;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering) slider {
  min-width: 4px;
  min-height: 4px;
  margin: 3px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering) button {
  min-width: 4px;
  min-height: 4px;
  margin: 3px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 9999px;
  background-color: rgba(0, 0, 0, 0.38);
  background-clip: padding-box;
  -gtk-icon-source: none;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering) button:disabled {
  background-color: rgba(0, 0, 0, 0.3);
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering).horizontal slider {
  min-width: 24px;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering).horizontal button {
  min-width: 8px;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering).vertical slider {
  min-height: 24px;
}

scrollbar.overlay-indicator:not(.dragging):not(.hovering).vertical button {
  min-height: 8px;
}

scrollbar.overlay-indicator.dragging, scrollbar.overlay-indicator.hovering {
  background-color: rgba(255, 255, 255, 0.9);
}

scrollbar.horizontal slider {
  min-width: 24px;
}

scrollbar.vertical slider {
  min-height: 24px;
}

scrollbar button {
  min-width: 16px;
  min-height: 16px;
  padding: 0;
  border-radius: 0;
}

scrollbar.vertical button.down {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

scrollbar.vertical button.up {
  -gtk-icon-source: -gtk-icontheme("pan-up-symbolic");
}

scrollbar.horizontal button.down {
  -gtk-icon-source: -gtk-icontheme("pan-end-symbolic");
}

scrollbar.horizontal button.up {
  -gtk-icon-source: -gtk-icontheme("pan-start-symbolic");
}

/**********
 * Switch *
 **********/
switch {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  margin: 6px 0;
  border: none;
  border-radius: 9999px;
  background-color: rgba(0, 0, 0, 0.38);
  background-clip: padding-box;
  font-size: 0;
  color: transparent;
}

switch:checked {
  background-color: @accent_color;
}

switch:disabled {
  opacity: 0.5;
}

switch slider {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  min-width: 18px;
  min-height: 18px;
  margin: 3px;
  border-radius: 9999px;
  outline: none;
  box-shadow: 0 3px 3px -2px rgba(0, 0, 0, 0.05), 0 2px 3px -1px rgba(0, 0, 0, 0.06), 0 1px 4px 0 rgba(0, 0, 0, 0.05);
  background-color: white;
  border: none;
  color: transparent;
}

switch:focus slider, switch:hover slider, switch:focus:hover slider {
  box-shadow: 0 0 0 6px rgba(0, 0, 0, 0.12);
}

/*************************
 * Check and Radio items *
 *************************/
.view.content-view.check:not(list),
.content-view .tile check:not(list) {
  min-height: 40px;
  min-width: 40px;
  margin: 0;
  padding: 0;
  box-shadow: none;
  background-color: transparent;
  background-image: none;
}

.view.content-view.check:not(list),
.content-view .tile check:not(list) {
  -gtk-icon-source: -gtk-scaled(url("assets/selectionmode-checkbox-unchecked.svg"), url("assets/<EMAIL>"));
}

.view.content-view.check:not(list):checked,
.content-view .tile check:not(list):checked {
  -gtk-icon-source: -gtk-scaled(url("assets/selectionmode-checkbox-checked.svg"), url("assets/<EMAIL>"));
}

checkbutton,
radiobutton {
  outline: none;
}

checkbutton.text-button,
radiobutton.text-button {
  padding: 2px;
}

checkbutton.text-button label:not(:only-child),
radiobutton.text-button label:not(:only-child) {
  margin: 0 4px;
}

check,
radio {
  min-height: 20px;
  min-width: 20px;
  margin: 3px;
  padding: 0;
  border-radius: 9999px;
  color: transparent;
  background-color: rgba(0, 0, 0, 0.12);
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 150ms cubic-bezier(0, 0, 0.2, 1);
}

check:hover,
radio:hover {
  box-shadow: 0 0 0 6px rgba(0, 0, 0, 0.04);
  background-color: rgba(0, 0, 0, 0.15);
}

check:active,
radio:active {
  box-shadow: 0 0 0 6px rgba(0, 0, 0, 0.12);
  background-color: rgba(0, 0, 0, 0.2);
}

check:focus,
radio:focus {
  outline: none;
}

check:disabled,
radio:disabled {
  background-color: rgba(0, 0, 0, 0.04);
}

check:checked, check:indeterminate,
radio:checked,
radio:indeterminate {
  color: #FFFFFF;
  background-color: @accent_color;
}

check:checked:hover, check:indeterminate:hover,
radio:checked:hover,
radio:indeterminate:hover {
  box-shadow: 0 0 0 6px alpha(@accent_color, 0.15);
  background-color: @accent_bg_color;
}

check:checked:active, check:indeterminate:active,
radio:checked:active,
radio:indeterminate:active {
  box-shadow: 0 0 0 6px alpha(@accent_color, 0.2);
  background-color: @accent_color;
}

check:checked:disabled, check:indeterminate:disabled,
radio:checked:disabled,
radio:indeterminate:disabled {
  color: rgba(255, 255, 255, 0.5);
  background-color: alpha(@accent_color, 0.35);
}

popover modelbutton.flat check, popover modelbutton.flat check:focus, popover modelbutton.flat check:hover, popover modelbutton.flat check:focus:hover, popover modelbutton.flat check:active, popover modelbutton.flat check:disabled, popover modelbutton.flat radio, popover modelbutton.flat radio:focus, popover modelbutton.flat radio:hover, popover modelbutton.flat radio:focus:hover, popover modelbutton.flat radio:active, popover modelbutton.flat radio:disabled {
  transition: none;
  box-shadow: none;
  background-image: none;
}

popover modelbutton.flat check.left:dir(rtl), popover modelbutton.flat radio.left:dir(rtl) {
  margin-left: -3px;
  margin-right: 6px;
}

popover modelbutton.flat check.right:dir(ltr), popover modelbutton.flat radio.right:dir(ltr) {
  margin-left: 6px;
  margin-right: -3px;
}

menu menuitem check, menu menuitem radio {
  transition: none;
  margin: 0;
  padding: 0;
}

menu menuitem check:dir(ltr), menu menuitem radio:dir(ltr) {
  margin-right: 6px;
  margin-left: -3px;
}

menu menuitem check:dir(rtl), menu menuitem radio:dir(rtl) {
  margin-left: 6px;
  margin-right: -3px;
}

menu menuitem check, menu menuitem check:hover, menu menuitem check:disabled, menu menuitem check:checked:hover, menu menuitem check:indeterminate:hover, menu menuitem radio, menu menuitem radio:hover, menu menuitem radio:disabled, menu menuitem radio:checked:hover, menu menuitem radio:indeterminate:hover {
  box-shadow: none;
}


check:checked {
  -gtk-icon-source: -gtk-recolor(url("assets/checkbox-checked-symbolic.svg"));
}


check:indeterminate {
  -gtk-icon-source: -gtk-recolor(url("assets/checkbox-mixed-symbolic.svg"));
}


radio:checked {
  -gtk-icon-source: -gtk-recolor(url("assets/radio-checked-symbolic.svg"));
}


radio:indeterminate {
  -gtk-icon-source: -gtk-recolor(url("assets/radio-mixed-symbolic.svg"));
}

#MozillaGtkWidget > widget > checkbutton > check,
menu menuitem check {
  min-height: 16px;
  min-width: 16px;
}

#MozillaGtkWidget > widget > checkbutton > check:checked,
menu menuitem check:checked {
  -gtk-icon-source: -gtk-recolor(url("assets/small-checkbox-checked-symbolic.svg"));
}

#MozillaGtkWidget > widget > checkbutton > check:indeterminate,
menu menuitem check:indeterminate {
  -gtk-icon-source: -gtk-recolor(url("assets/small-checkbox-mixed-symbolic.svg"));
}

#MozillaGtkWidget > widget > radiobutton > radio,
menu menuitem radio {
  min-height: 16px;
  min-width: 16px;
}

#MozillaGtkWidget > widget > radiobutton > radio:checked,
menu menuitem radio:checked {
  -gtk-icon-source: -gtk-recolor(url("assets/small-radio-checked-symbolic.svg"));
}

#MozillaGtkWidget > widget > radiobutton > radio:indeterminate,
menu menuitem radio:indeterminate {
  -gtk-icon-source: -gtk-recolor(url("assets/small-radio-mixed-symbolic.svg"));
}

check:not(:checked):active {
  -gtk-icon-transform: rotate(90deg);
}

check:not(:checked):indeterminate:active,
radio:not(:checked):indeterminate:active {
  -gtk-icon-transform: scaleX(-1);
}

treeview.view radio, treeview.view check {
  padding: 0;
  margin: 0;
}

treeview.view radio:not(:disabled):not(:checked):not(:indeterminate), treeview.view check:not(:disabled):not(:checked):not(:indeterminate) {
  background-color: rgba(0, 0, 0, 0.12);
}

treeview.view radio, treeview.view radio:hover, treeview.view radio:disabled, treeview.view radio:checked:hover, treeview.view radio:indeterminate:hover, treeview.view check, treeview.view check:hover, treeview.view check:disabled, treeview.view check:checked:hover, treeview.view check:indeterminate:hover {
  box-shadow: none;
}

treeview.view radio:checked, treeview.view radio:indeterminate, treeview.view check:checked, treeview.view check:indeterminate, treeview.view:hover radio:checked, treeview.view:hover radio:indeterminate, treeview.view:hover check:checked, treeview.view:hover check:indeterminate, treeview.view:selected radio:checked, treeview.view:selected radio:indeterminate, treeview.view:selected check:checked, treeview.view:selected check:indeterminate {
  background-color: @accent_color;
  color: #FFFFFF;
  background-image: none;
}

/************
 * GtkScale *
 ************/
scale {
  min-height: 2px;
  min-width: 2px;
}

scale.horizontal {
  padding: 16px 12px;
}

scale.vertical {
  padding: 12px 16px;
}

scale slider {
  min-height: 18px;
  min-width: 18px;
  margin: -8px;
}

scale.fine-tune.horizontal {
  min-height: 4px;
  padding-top: 15px;
  padding-bottom: 15px;
}

scale.fine-tune.vertical {
  min-width: 4px;
  padding-left: 15px;
  padding-right: 15px;
}

scale.fine-tune slider {
  margin: -7px;
}

scale trough {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  outline: none;
  background-color: rgba(0, 0, 0, 0.26);
}

scale trough:disabled {
  background-color: rgba(0, 0, 0, 0.12);
}

scale highlight {
  transition: background-image 75ms cubic-bezier(0, 0, 0.2, 1);
  background-image: image(@accent_color);
}

scale highlight:disabled {
  background-color: #FFFFFF;
  background-image: image(rgba(0, 0, 0, 0.3));
}

scale fill {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  background-color: rgba(0, 0, 0, 0.26);
}

scale fill:disabled {
  background-color: transparent;
}

scale slider {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  border-radius: 9999px;
  color: @accent_color;
  background-color: #FFFFFF;
  box-shadow: inset 0 0 0 2px @accent_color;
}

scale slider:hover {
  box-shadow: inset 0 0 0 2px @accent_color, 0 0 0 8px rgba(0, 0, 0, 0.12);
}

scale slider:active {
  box-shadow: inset 0 0 0 4px @accent_color, 0 0 0 8px rgba(0, 0, 0, 0.12);
}

scale slider:disabled {
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.3);
}

scale marks,
scale value {
  color: rgba(0, 0, 0, 0.6);
}

scale indicator {
  background-color: rgba(0, 0, 0, 0.26);
  color: transparent;
}

scale.horizontal marks.top {
  margin-bottom: 7px;
  margin-top: -15px;
}

scale.horizontal.fine-tune marks.top {
  margin-bottom: 6px;
  margin-top: -14px;
}

scale.horizontal marks.bottom {
  margin-top: 7px;
  margin-bottom: -15px;
}

scale.horizontal.fine-tune marks.bottom {
  margin-top: 6px;
  margin-bottom: -14px;
}

scale.vertical marks.top {
  margin-right: 7px;
  margin-left: -15px;
}

scale.vertical.fine-tune marks.top {
  margin-right: 6px;
  margin-left: -14px;
}

scale.vertical marks.bottom {
  margin-left: 7px;
  margin-right: -15px;
}

scale.vertical.fine-tune marks.bottom {
  margin-left: 6px;
  margin-right: -14px;
}

scale.horizontal indicator {
  min-height: 8px;
  min-width: 1px;
}

scale.vertical indicator {
  min-height: 1px;
  min-width: 8px;
}

scale.horizontal.marks-before:not(.marks-after) slider {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  min-height: 32px;
  min-width: 32px;
  margin: -15px;
  border-radius: 50%;
  background-size: auto, 1000% 1000%;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: transparent;
}

scale.horizontal.marks-before:not(.marks-after) slider, scale.horizontal.marks-before:not(.marks-after) slider:hover, scale.horizontal.marks-before:not(.marks-after) slider:active, scale.horizontal.marks-before:not(.marks-after) slider:disabled {
  box-shadow: none;
}

scale.horizontal.marks-before:not(.marks-after) slider:focus {
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-before:not(.marks-after) slider:hover {
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-before:not(.marks-after) slider:focus:hover {
  background-color: alpha(currentColor, 0.16);
}

scale.horizontal.marks-before:not(.marks-after) slider:active {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  animation: ripple-on-slider 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-size: auto, 0% 0%;
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-before:not(.marks-after) slider {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-before-slider.svg"), url("assets/<EMAIL>")), radial-gradient(circle, transparent 10%, transparent 0%);
}

scale.horizontal.marks-before:not(.marks-after) slider:disabled {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-before-slider-disabled.svg"), url("assets/<EMAIL>")), radial-gradient(circle, transparent 10%, transparent 0%);
}

scale.horizontal.marks-before:not(.marks-after) slider:active {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-before-slider.svg"), url("assets/<EMAIL>")), radial-gradient(circle, alpha(currentColor, 0.12) 10%, transparent 0%);
}

scale.horizontal.marks-after:not(.marks-before) slider {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  min-height: 32px;
  min-width: 32px;
  margin: -15px;
  border-radius: 50%;
  background-size: auto, 1000% 1000%;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: transparent;
}

scale.horizontal.marks-after:not(.marks-before) slider, scale.horizontal.marks-after:not(.marks-before) slider:hover, scale.horizontal.marks-after:not(.marks-before) slider:active, scale.horizontal.marks-after:not(.marks-before) slider:disabled {
  box-shadow: none;
}

scale.horizontal.marks-after:not(.marks-before) slider:focus {
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-after:not(.marks-before) slider:hover {
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-after:not(.marks-before) slider:focus:hover {
  background-color: alpha(currentColor, 0.16);
}

scale.horizontal.marks-after:not(.marks-before) slider:active {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  animation: ripple-on-slider 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-size: auto, 0% 0%;
  background-color: alpha(currentColor, 0.08);
}

scale.horizontal.marks-after:not(.marks-before) slider {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-after-slider.svg"), url("assets/<EMAIL>")), radial-gradient(circle, transparent 10%, transparent 0%);
}

scale.horizontal.marks-after:not(.marks-before) slider:disabled {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-after-slider-disabled.svg"), url("assets/<EMAIL>")), radial-gradient(circle, transparent 10%, transparent 0%);
}

scale.horizontal.marks-after:not(.marks-before) slider:active {
  background-image: -gtk-scaled(url("assets/scale-horz-marks-after-slider.svg"), url("assets/<EMAIL>")), radial-gradient(circle, alpha(currentColor, 0.12) 10%, transparent 0%);
}

scale.vertical.marks-before:not(.marks-after) slider {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  min-height: 32px;
  min-width: 32px;
  margin: -15px;
  border-radius: 50%;
  background-size: auto, 1000% 1000%;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: transparent;
}

scale.vertical.marks-before:not(.marks-after) slider, scale.vertical.marks-before:not(.marks-after) slider:hover, scale.vertical.marks-before:not(.marks-after) slider:active, scale.vertical.marks-before:not(.marks-after) slider:disabled {
  box-shadow: none;
}

scale.vertical.marks-before:not(.marks-after) slider:focus {
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-before:not(.marks-after) slider:hover {
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-before:not(.marks-after) slider:focus:hover {
  background-color: alpha(currentColor, 0.16);
}

scale.vertical.marks-before:not(.marks-after) slider:active {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  animation: ripple-on-slider 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-size: auto, 0% 0%;
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-before:not(.marks-after) slider {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-before-slider.svg"), url("assets/<EMAIL>")), radial-gradient(circle, transparent 10%, transparent 0%);
}

scale.vertical.marks-before:not(.marks-after) slider:disabled {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-before-slider-disabled.svg"), url("assets/<EMAIL>")), radial-gradient(circle, transparent 10%, transparent 0%);
}

scale.vertical.marks-before:not(.marks-after) slider:active {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-before-slider.svg"), url("assets/<EMAIL>")), radial-gradient(circle, alpha(currentColor, 0.12) 10%, transparent 0%);
}

scale.vertical.marks-after:not(.marks-before) slider {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  min-height: 32px;
  min-width: 32px;
  margin: -15px;
  border-radius: 50%;
  background-size: auto, 1000% 1000%;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: transparent;
}

scale.vertical.marks-after:not(.marks-before) slider, scale.vertical.marks-after:not(.marks-before) slider:hover, scale.vertical.marks-after:not(.marks-before) slider:active, scale.vertical.marks-after:not(.marks-before) slider:disabled {
  box-shadow: none;
}

scale.vertical.marks-after:not(.marks-before) slider:focus {
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-after:not(.marks-before) slider:hover {
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-after:not(.marks-before) slider:focus:hover {
  background-color: alpha(currentColor, 0.16);
}

scale.vertical.marks-after:not(.marks-before) slider:active {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  animation: ripple-on-slider 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  background-size: auto, 0% 0%;
  background-color: alpha(currentColor, 0.08);
}

scale.vertical.marks-after:not(.marks-before) slider {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-after-slider.svg"), url("assets/<EMAIL>")), radial-gradient(circle, transparent 10%, transparent 0%);
}

scale.vertical.marks-after:not(.marks-before) slider:disabled {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-after-slider-disabled.svg"), url("assets/<EMAIL>")), radial-gradient(circle, transparent 10%, transparent 0%);
}

scale.vertical.marks-after:not(.marks-before) slider:active {
  background-image: -gtk-scaled(url("assets/scale-vert-marks-after-slider.svg"), url("assets/<EMAIL>")), radial-gradient(circle, alpha(currentColor, 0.12) 10%, transparent 0%);
}

scale.color {
  min-height: 0;
  min-width: 0;
}

scale.color.horizontal {
  padding: 0 0 12px 0;
}

scale.color.horizontal slider:dir(ltr), scale.color.horizontal slider:dir(rtl) {
  margin-bottom: -13.5px;
  margin-top: 11.5px;
}

scale.color.vertical:dir(ltr) {
  padding: 0 0 0 12px;
}

scale.color.vertical:dir(ltr) slider {
  margin-left: -13.5px;
  margin-right: 11.5px;
}

scale.color.vertical:dir(rtl) {
  padding: 0 12px 0 0;
}

scale.color.vertical:dir(rtl) slider {
  margin-right: -13.5px;
  margin-left: 11.5px;
}

/*****************
 * Progress bars *
 *****************/
progressbar {
  color: rgba(0, 0, 0, 0.6);
  font-size: smaller;
}

progressbar.horizontal trough,
progressbar.horizontal progress {
  min-height: 6px;
}

progressbar.vertical trough,
progressbar.vertical progress {
  min-width: 6px;
}

progressbar trough {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.12);
}

progressbar progress {
  border-radius: 6px;
  background-color: @accent_color;
}

progressbar.osd {
  min-width: 6px;
  min-height: 6px;
  background-color: transparent;
}

progressbar.osd trough {
  background-color: transparent;
}

progressbar.osd progress {
  background-color: @accent_color;
}

progressbar trough.empty progress {
  all: unset;
}

/*************
 * Level Bar *
 *************/
levelbar.horizontal block {
  min-height: 6px;
}

levelbar.horizontal.discrete block {
  min-width: 34px;
}

levelbar.horizontal.discrete block:not(:last-child) {
  margin-right: 2px;
}

levelbar.vertical block {
  min-width: 6px;
}

levelbar.vertical.discrete block {
  min-height: 34px;
}

levelbar.vertical.discrete block:not(:last-child) {
  margin-bottom: 2px;
}

levelbar trough {
  border-radius: 6px;
}

levelbar block.low {
  background-color: #FBC02D;
}

levelbar block.high, levelbar block:not(.empty) {
  background-color: @accent_color;
}

levelbar block.full {
  background-color: #4CAF50;
}

levelbar block.empty {
  background-color: rgba(0, 0, 0, 0.12);
}

/****************
 * Print dialog *
*****************/
printdialog paper {
  padding: 0;
  border: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
  color: rgba(0, 0, 0, 0.87);
}

printdialog .dialog-action-box {
  margin: 12px;
}

/**********
 * Frames *
 **********/
frame > border, .frame {
  margin: 0;
  padding: 0;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 0;
  box-shadow: none;
}

.frame.view {
  border-radius: 6px;
}

.frame.flat {
  border-style: none;
}

frame.flat > border, frame > border.flat, statusbar frame > border {
  border: none;
}

actionbar > revealer > box {
  padding: 6px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
  background-clip: border-box;
}

.background.csd box.vertical > actionbar > revealer > box .background.csd box.vertical > revealer > actionbar > revealer > box {
  border-radius: 0 0 12px 12px;
}

scrolledwindow viewport.frame {
  border: none;
}

stack scrolledwindow.frame viewport.frame list {
  border: none;
}

overshoot.top {
  background-image: -gtk-gradient(radial, center top, 0, center top, 0.5, to(rgba(0, 0, 0, 0.12)), to(rgba(0, 0, 0, 0))), -gtk-gradient(radial, center top, 0, center top, 0.6, from(rgba(0, 0, 0, 0.07)), to(rgba(0, 0, 0, 0)));
  background-size: 100% 5%, 100% 100%;
  background-repeat: no-repeat;
  background-position: center top;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

overshoot.bottom {
  background-image: -gtk-gradient(radial, center bottom, 0, center bottom, 0.5, to(rgba(0, 0, 0, 0.12)), to(rgba(0, 0, 0, 0))), -gtk-gradient(radial, center bottom, 0, center bottom, 0.6, from(rgba(0, 0, 0, 0.07)), to(rgba(0, 0, 0, 0)));
  background-size: 100% 5%, 100% 100%;
  background-repeat: no-repeat;
  background-position: center bottom;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

overshoot.left {
  background-image: -gtk-gradient(radial, left center, 0, left center, 0.5, to(rgba(0, 0, 0, 0.12)), to(rgba(0, 0, 0, 0))), -gtk-gradient(radial, left center, 0, left center, 0.6, from(rgba(0, 0, 0, 0.07)), to(rgba(0, 0, 0, 0)));
  background-size: 5% 100%, 100% 100%;
  background-repeat: no-repeat;
  background-position: left center;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

overshoot.right {
  background-image: -gtk-gradient(radial, right center, 0, right center, 0.5, to(rgba(0, 0, 0, 0.12)), to(rgba(0, 0, 0, 0))), -gtk-gradient(radial, right center, 0, right center, 0.6, from(rgba(0, 0, 0, 0.07)), to(rgba(0, 0, 0, 0)));
  background-size: 5% 100%, 100% 100%;
  background-repeat: no-repeat;
  background-position: right center;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

junction {
  border-style: solid none none solid;
  border-width: 1px;
  border-color: rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
}

junction:dir(rtl) {
  border-style: solid solid none none;
}

separator {
  min-width: 1px;
  min-height: 1px;
  background-color: rgba(0, 0, 0, 0.12);
}

preferences stacksidebar.sidebar list separator, hdyleaflet > box.vertical > scrolledwindow > viewport.frame list:not(.view):not(.tweak-group):not(.tweak-group-startup) separator, leaflet > box.vertical > scrolledwindow > viewport.frame list:not(.view):not(.tweak-group):not(.tweak-group-startup) separator, box.horizontal > stack.background > box.vertical > scrolledwindow > viewport.frame list:not(.view):not(.tweak-group):not(.tweak-group-startup) separator, window.background.csd > leaflet > box.vertical > scrolledwindow.view > viewport.frame > stack list separator,
window.background.csd > hdyleaflet > box.vertical > scrolledwindow.view > viewport.frame > stack list separator,
window.background.csd > box.horizontal > box.vertical > scrolledwindow.view > viewport.frame > stack list separator, .tweak-categories separator, placessidebar.sidebar list > separator, stacksidebar.sidebar + separator.vertical,
stacksidebar.sidebar separator.horizontal, button.font separator, button.file separator {
  min-width: 0;
  min-height: 0;
  background-color: transparent;
}

/*********
 * Lists *
 *********/
window.background.csd stack stack stack frame > list,
window.background.csd > stack > stack > box > frame > list,
window.background.csd > stack > stack > box > box > frame > list,
window.background.csd > stack > box > stack > box > frame > list,
window.background.csd > stack > box > stack > scrolledwindow > viewport frame > list,
window.background.csd > stack > box > stack > box > scrolledwindow > viewport > frame > list,
window.background.csd > stack > grid > scrolledwindow > viewport > box > frame > list, window.background.csd > stack > list,
window.background.csd > stack > scrolledwindow > viewport > box > list,
window.background.csd > box > stack > scrolledwindow > viewport > box > list, preferencesgroup list, .geary-accounts-editor-pane list, window.background.csd.unified > deck > deck > deck list, hdyleaflet list.view, hdyleaflet list.frame, leaflet list.view, leaflet list.frame, box.horizontal > stack.background list.view, box.horizontal > stack.background list.frame, hdyleaflet stack.background scrolledwindow > viewport list, hdyleaflet overlay scrolledwindow > viewport list, leaflet stack.background scrolledwindow > viewport list, leaflet overlay scrolledwindow > viewport list, box.horizontal > stack.background stack.background scrolledwindow > viewport list, box.horizontal > stack.background overlay scrolledwindow > viewport list, hdyleaflet frame:not(.view) list:not(.contacts-contact-list), leaflet frame:not(.view) list:not(.contacts-contact-list), box.horizontal > stack.background frame:not(.view) list:not(.contacts-contact-list), list.tweak-group list, list#ListBoxTweakGroup list, .tweak-group-startup, list.content:not(.conversation-listbox) {
  border-radius: 7px;
  box-shadow: none;
  border: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
}

window.background.csd stack stack stack frame > list > separator,
window.background.csd > stack > stack > box > frame > list > separator,
window.background.csd > stack > box > stack > scrolledwindow > viewport frame > list > separator,
window.background.csd > stack > grid > scrolledwindow > viewport > box > frame > list > separator, window.background.csd > stack > list > separator,
window.background.csd > stack > scrolledwindow > viewport > box > list > separator, preferencesgroup list > separator, .geary-accounts-editor-pane list > separator, window.background.csd.unified > deck > deck > deck list > separator, hdyleaflet list.view > separator, hdyleaflet list.frame > separator, leaflet list.view > separator, leaflet list.frame > separator, box.horizontal > stack.background list.view > separator, box.horizontal > stack.background list.frame > separator, hdyleaflet stack.background scrolledwindow > viewport list > separator, hdyleaflet overlay scrolledwindow > viewport list > separator, leaflet stack.background scrolledwindow > viewport list > separator, leaflet overlay scrolledwindow > viewport list > separator, box.horizontal > stack.background stack.background scrolledwindow > viewport list > separator, box.horizontal > stack.background overlay scrolledwindow > viewport list > separator, hdyleaflet frame:not(.view) list:not(.contacts-contact-list) > separator, leaflet frame:not(.view) list:not(.contacts-contact-list) > separator, box.horizontal > stack.background frame:not(.view) list:not(.contacts-contact-list) > separator, list.tweak-group list > separator, list#ListBoxTweakGroup list > separator, .tweak-group-startup > separator, list.content:not(.conversation-listbox) > separator {
  background: none;
  min-height: 0;
}

window.background.csd stack stack stack frame > list row,
window.background.csd > stack > stack > box > frame > list row,
window.background.csd > stack > box > stack > scrolledwindow > viewport frame > list row,
window.background.csd > stack > grid > scrolledwindow > viewport > box > frame > list row, window.background.csd > stack > list row,
window.background.csd > stack > scrolledwindow > viewport > box > list row, preferencesgroup list row, .geary-accounts-editor-pane list row, window.background.csd.unified > deck > deck > deck list row, hdyleaflet list.view row, hdyleaflet list.frame row, leaflet list.view row, leaflet list.frame row, box.horizontal > stack.background list.view row, box.horizontal > stack.background list.frame row, hdyleaflet stack.background scrolledwindow > viewport list row, hdyleaflet overlay scrolledwindow > viewport list row, leaflet stack.background scrolledwindow > viewport list row, leaflet overlay scrolledwindow > viewport list row, box.horizontal > stack.background stack.background scrolledwindow > viewport list row, box.horizontal > stack.background overlay scrolledwindow > viewport list row, hdyleaflet frame:not(.view) list:not(.contacts-contact-list) row, leaflet frame:not(.view) list:not(.contacts-contact-list) row, box.horizontal > stack.background frame:not(.view) list:not(.contacts-contact-list) row, list.tweak-group list row, list#ListBoxTweakGroup list row, .tweak-group-startup > row, list.content:not(.conversation-listbox) > row {
  border-radius: 0;
}

window.background.csd stack stack stack frame > list row:not(:first-child), window.background.csd > stack > list row:not(:first-child), preferencesgroup list row:not(:first-child), .geary-accounts-editor-pane list row:not(:first-child), window.background.csd.unified > deck > deck > deck list row:not(:first-child), hdyleaflet list.view row:not(:first-child), hdyleaflet list.frame row:not(:first-child), leaflet list.view row:not(:first-child), leaflet list.frame row:not(:first-child), box.horizontal > stack.background list.view row:not(:first-child), box.horizontal > stack.background list.frame row:not(:first-child), hdyleaflet stack.background scrolledwindow > viewport list row:not(:first-child), hdyleaflet overlay scrolledwindow > viewport list row:not(:first-child), leaflet stack.background scrolledwindow > viewport list row:not(:first-child), leaflet overlay scrolledwindow > viewport list row:not(:first-child), box.horizontal > stack.background stack.background scrolledwindow > viewport list row:not(:first-child), box.horizontal > stack.background overlay scrolledwindow > viewport list row:not(:first-child), hdyleaflet frame:not(.view) list:not(.contacts-contact-list) row:not(:first-child), leaflet frame:not(.view) list:not(.contacts-contact-list) row:not(:first-child), box.horizontal > stack.background frame:not(.view) list:not(.contacts-contact-list) row:not(:first-child), list.tweak-group list row:not(:first-child), list#ListBoxTweakGroup list row:not(:first-child), .tweak-group-startup > row:not(:first-child), list.content:not(.conversation-listbox) > row:not(:first-child) {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

window.background.csd stack stack stack frame > list row:first-child, window.background.csd > stack > list row:first-child, preferencesgroup list row:first-child, .geary-accounts-editor-pane list row:first-child, window.background.csd.unified > deck > deck > deck list row:first-child, hdyleaflet list.view row:first-child, hdyleaflet list.frame row:first-child, leaflet list.view row:first-child, leaflet list.frame row:first-child, box.horizontal > stack.background list.view row:first-child, box.horizontal > stack.background list.frame row:first-child, hdyleaflet stack.background scrolledwindow > viewport list row:first-child, hdyleaflet overlay scrolledwindow > viewport list row:first-child, leaflet stack.background scrolledwindow > viewport list row:first-child, leaflet overlay scrolledwindow > viewport list row:first-child, box.horizontal > stack.background stack.background scrolledwindow > viewport list row:first-child, box.horizontal > stack.background overlay scrolledwindow > viewport list row:first-child, hdyleaflet frame:not(.view) list:not(.contacts-contact-list) row:first-child, leaflet frame:not(.view) list:not(.contacts-contact-list) row:first-child, box.horizontal > stack.background frame:not(.view) list:not(.contacts-contact-list) row:first-child, list.tweak-group list row:first-child, list#ListBoxTweakGroup list row:first-child, .tweak-group-startup > row:first-child, list.content:not(.conversation-listbox) > row:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

window.background.csd stack stack stack frame > list row:last-child, window.background.csd > stack > list row:last-child, preferencesgroup list row:last-child, .geary-accounts-editor-pane list row:last-child, window.background.csd.unified > deck > deck > deck list row:last-child, hdyleaflet list.view row:last-child, hdyleaflet list.frame row:last-child, leaflet list.view row:last-child, leaflet list.frame row:last-child, box.horizontal > stack.background list.view row:last-child, box.horizontal > stack.background list.frame row:last-child, hdyleaflet stack.background scrolledwindow > viewport list row:last-child, hdyleaflet overlay scrolledwindow > viewport list row:last-child, leaflet stack.background scrolledwindow > viewport list row:last-child, leaflet overlay scrolledwindow > viewport list row:last-child, box.horizontal > stack.background stack.background scrolledwindow > viewport list row:last-child, box.horizontal > stack.background overlay scrolledwindow > viewport list row:last-child, hdyleaflet frame:not(.view) list:not(.contacts-contact-list) row:last-child, leaflet frame:not(.view) list:not(.contacts-contact-list) row:last-child, box.horizontal > stack.background frame:not(.view) list:not(.contacts-contact-list) row:last-child, list.tweak-group list row:last-child, list#ListBoxTweakGroup list row:last-child, .tweak-group-startup > row:last-child, list.content:not(.conversation-listbox) > row:last-child {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

window.background.csd stack stack stack frame > list row:only-child, window.background.csd > stack > list row:only-child, preferencesgroup list row:only-child, .geary-accounts-editor-pane list row:only-child, window.background.csd.unified > deck > deck > deck list row:only-child, hdyleaflet list.view row:only-child, hdyleaflet list.frame row:only-child, leaflet list.view row:only-child, leaflet list.frame row:only-child, box.horizontal > stack.background list.view row:only-child, box.horizontal > stack.background list.frame row:only-child, hdyleaflet stack.background scrolledwindow > viewport list row:only-child, hdyleaflet overlay scrolledwindow > viewport list row:only-child, leaflet stack.background scrolledwindow > viewport list row:only-child, leaflet overlay scrolledwindow > viewport list row:only-child, box.horizontal > stack.background stack.background scrolledwindow > viewport list row:only-child, box.horizontal > stack.background overlay scrolledwindow > viewport list row:only-child, hdyleaflet frame:not(.view) list:not(.contacts-contact-list) row:only-child, leaflet frame:not(.view) list:not(.contacts-contact-list) row:only-child, box.horizontal > stack.background frame:not(.view) list:not(.contacts-contact-list) row:only-child, list.tweak-group list row:only-child, list#ListBoxTweakGroup list row:only-child, .tweak-group-startup > row:only-child, list.content:not(.conversation-listbox) > row:only-child {
  border-radius: 6px;
}

window.background.csd stack stack stack frame > list row:focus, window.background.csd > stack > list row:focus, preferencesgroup list row:focus, .geary-accounts-editor-pane list row:focus, window.background.csd.unified > deck > deck > deck list row:focus, hdyleaflet list.view row:focus, hdyleaflet list.frame row:focus, leaflet list.view row:focus, leaflet list.frame row:focus, box.horizontal > stack.background list.view row:focus, box.horizontal > stack.background list.frame row:focus, hdyleaflet stack.background scrolledwindow > viewport list row:focus, hdyleaflet overlay scrolledwindow > viewport list row:focus, leaflet stack.background scrolledwindow > viewport list row:focus, leaflet overlay scrolledwindow > viewport list row:focus, box.horizontal > stack.background stack.background scrolledwindow > viewport list row:focus, box.horizontal > stack.background overlay scrolledwindow > viewport list row:focus, hdyleaflet frame:not(.view) list:not(.contacts-contact-list) row:focus, leaflet frame:not(.view) list:not(.contacts-contact-list) row:focus, box.horizontal > stack.background frame:not(.view) list:not(.contacts-contact-list) row:focus, list.tweak-group list row:focus, list#ListBoxTweakGroup list row:focus, .tweak-group-startup > row:focus, list.content:not(.conversation-listbox) > row:focus {
  box-shadow: inset 0 0 0 1000px alpha(currentColor, 0.05);
}

list {
  border-color: rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
}

list row {
  padding: 3px;
}

list.navigation-sidebar {
  padding: 3px;
}

list.navigation-sidebar > row {
  border-radius: 6px;
}

paned scrolledwindow > viewport.frame > list {
  background-color: transparent;
}

.budgie-popover scrolledwindow.sidebar:not(.categories) list > row.activatable, treeview.view header button, row.activatable {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  outline: none;
  box-shadow: inset 0 0 0 9999px transparent;
  background-image: radial-gradient(circle, transparent 10%, transparent 0%);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1000% 1000%;
}

.budgie-popover scrolledwindow.sidebar:not(.categories) list > row.activatable:focus, treeview.view header button:focus, row.activatable:focus {
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.05);
}

.budgie-popover scrolledwindow.sidebar:not(.categories) list > row.activatable:hover, treeview.view header button:hover, row.activatable:hover {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 0ms, background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.05);
}

.budgie-popover scrolledwindow.sidebar:not(.categories) list > row.has-open-popup.activatable, treeview.view header button.has-open-popup, .budgie-popover scrolledwindow.sidebar:not(.categories) list > row.activatable:active, treeview.view header button:active, row.activatable.has-open-popup, row.activatable:active {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.05);
  background-image: radial-gradient(circle, alpha(currentColor, 0.05) 10%, transparent 0%);
  background-size: 0% 0%;
}

row:selected {
  color: inherit;
  background-color: alpha(currentColor, 0.06);
}

row:selected image,
row:selected label {
  color: rgba(0, 0, 0, 0.87);
}

row:selected button image,
row:selected button label {
  color: inherit;
}

row:selected:disabled image,
row:selected:disabled label {
  color: rgba(0, 0, 0, 0.38);
}

/*********************
 * App Notifications *
 *********************/
.app-notification {
  margin: 8px;
  padding: 6px 15px;
}

.app-notification button.flat:last-child {
  margin-right: -9px;
}

.app-notification button.text-button:not(:disabled) {
  color: @accent_color;
}

.app-notification.frame,
.app-notification border {
  border-style: none;
}

/*************
 * Expanders *
 *************/
expander title > arrow {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  min-width: 16px;
  min-height: 16px;
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
  -gtk-icon-transform: rotate(-90deg);
  color: rgba(0, 0, 0, 0.6);
}

expander title > arrow:dir(rtl) {
  -gtk-icon-transform: rotate(90deg);
}

expander title > arrow:checked {
  -gtk-icon-transform: unset;
}

expander title > arrow:hover, expander title > arrow:active {
  color: rgba(0, 0, 0, 0.87);
}

expander title > arrow:disabled {
  color: rgba(0, 0, 0, 0.3);
}

/************
 * Calendar *
 ************/
calendar {
  padding: 1px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.87);
}

calendar:disabled {
  color: rgba(0, 0, 0, 0.38);
}

calendar:selected {
  border-radius: 7px;
}

calendar.header {
  border-style: none none solid;
  border-color: rgba(0, 0, 0, 0.12);
  border-radius: 0;
}

calendar.highlight {
  color: rgba(0, 0, 0, 0.6);
  font-weight: 500;
}

calendar:indeterminate {
  color: rgba(0, 0, 0, 0.3);
}

/***********
 * Dialogs *
 ***********/
messagedialog.background {
  background-color: #FFFFFF;
}

messagedialog.background .titlebar, messagedialog.background .titlebar:backdrop {
  background-color: #FFFFFF;
}

messagedialog.background.csd {
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  background-color: #FFFFFF;
}

messagedialog.background.csd .titlebar, messagedialog.background.csd .titlebar:backdrop {
  background-color: #FFFFFF;
}

messagedialog .titlebar, messagedialog.background.csd .titlebar {
  border: none;
  box-shadow: none;
}

messagedialog .dialog-action-box {
  margin-top: -6px;
}

messagedialog .dialog-action-box button, messagedialog .dialog-action-box button:first-child, messagedialog .dialog-action-box button:last-child, messagedialog .dialog-action-box .linked:not(.vertical) > button, messagedialog .dialog-action-box .linked:not(.vertical) > button:first-child, messagedialog .dialog-action-box .linked:not(.vertical) > button:last-child {
  border-radius: 6px;
}

messagedialog .dialog-action-box button:not(:last-child), messagedialog .dialog-action-box .linked:not(.vertical) > button:not(:last-child) {
  margin-right: 6px;
}

messagedialog .dialog-action-box button.suggested-action:not(:disabled), messagedialog .dialog-action-box .linked:not(.vertical) > button.suggested-action:not(:disabled) {
  color: @accent_color;
}

messagedialog .dialog-action-box button.destructive-action:not(:disabled), messagedialog .dialog-action-box .linked:not(.vertical) > button.destructive-action:not(:disabled) {
  color: #E53935;
}

.csd filechooser {
  background-color: #FFFFFF;
  border-radius: 0 0 12px 12px;
}

filechooser .dialog-action-box {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

filechooser #pathbarbox {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
}

filechooser stack.view {
  background-color: transparent;
  padding: 0;
}

filechooser stack.view scrolledwindow {
  background-color: transparent;
  border-radius: 0 0 12px 0;
}

filechooser stack.view scrolledwindow list {
  background-color: transparent;
}

filechooser stack.view > placesview {
  background-color: transparent;
}

filechooser stack.view > placesview > actionbar, filechooser stack.view > placesview > actionbar > revealer > box {
  background-color: transparent;
}

filechooser stack.view frame > border {
  border: none;
}

.csd filechooser placessidebar {
  background: none;
  border-bottom-left-radius: 12px;
}

filechooser actionbar, filechooser actionbar > revealer > box {
  background-color: transparent;
}

/***********
 * Sidebar *
 ***********/
.sidebar {
  border-style: none;
}

stacksidebar.sidebar:dir(ltr) list, stacksidebar.sidebar.left list, stacksidebar.sidebar.left:dir(rtl) list, .sidebar:not(separator):dir(ltr), .sidebar:not(separator).left {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  border-left-style: none;
}

stacksidebar.sidebar:dir(rtl) list, stacksidebar.sidebar.right list, .sidebar:not(separator):dir(rtl), .sidebar:not(separator).right {
  border-left: 1px solid rgba(0, 0, 0, 0.12);
  border-right-style: none;
}

.sidebar list {
  background-color: transparent;
}

paned .sidebar.left, paned .sidebar.right, paned .sidebar.left:dir(rtl), paned .sidebar:dir(rtl), paned .sidebar:dir(ltr), paned .sidebar {
  border-style: none;
}

stacksidebar.sidebar list {
  padding: 3px;
  background-color: #FFFFFF;
}

stacksidebar.sidebar row {
  min-height: 34px;
  padding: 0 3px;
  border-radius: 6px;
}

stacksidebar.sidebar row + row {
  margin-top: 3px;
}

stacksidebar.sidebar row:selected {
  background-color: @accent_color;
  color: #FFFFFF;
  font-weight: 500;
}

stacksidebar.sidebar row:selected label, stacksidebar.sidebar row:selected image {
  color: #FFFFFF;
}

stacksidebar.sidebar row > label {
  padding-left: 6px;
  padding-right: 6px;
  color: inherit;
}

/****************
 * File chooser *
 ****************/
row image.sidebar-icon {
  transition: color 75ms cubic-bezier(0, 0, 0.2, 1);
  color: rgba(0, 0, 0, 0.6);
}

row image.sidebar-icon:disabled {
  color: rgba(0, 0, 0, 0.3);
}

placessidebar.sidebar > viewport.frame {
  border-style: none;
}

placessidebar.sidebar list {
  padding: 6px;
}

placessidebar.sidebar row {
  min-height: 34px;
  margin: 0;
  padding: 0;
  border-radius: 6px;
}

placessidebar.sidebar row + row {
  margin-top: 3px;
}

placessidebar.sidebar row > revealer {
  padding: 0 8px 0 16px;
}

placessidebar.sidebar row:selected {
  background-color: rgba(0, 0, 0, 0.12);
  font-weight: 500;
}

placessidebar.sidebar row:disabled {
  color: rgba(0, 0, 0, 0.38);
}

placessidebar.sidebar row image.sidebar-icon:dir(ltr) {
  padding-right: 8px;
}

placessidebar.sidebar row image.sidebar-icon:dir(rtl) {
  padding-left: 8px;
}

placessidebar.sidebar row label.sidebar-label {
  color: inherit;
}

placessidebar.sidebar row label.sidebar-label:dir(ltr) {
  padding-right: 2px;
}

placessidebar.sidebar row label.sidebar-label:dir(rtl) {
  padding-left: 2px;
}

placessidebar.sidebar row.sidebar-placeholder-row {
  background-color: alpha(currentColor, 0.08);
}

placessidebar.sidebar row.sidebar-new-bookmark-row {
  color: @accent_color;
}

placessidebar.sidebar row.sidebar-new-bookmark-row image.sidebar-icon {
  color: @accent_color;
}

placessidebar.sidebar row:drop(active) {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 0ms, background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.08);
}

placesview .server-list-button > image {
  -gtk-icon-transform: rotate(0turn);
}

placesview .server-list-button:checked > image {
  -gtk-icon-transform: rotate(-0.5turn);
}

placesview > actionbar > revealer > box > label {
  padding-left: 8px;
  padding-right: 8px;
}

/*********
 * Paned *
 *********/
paned > separator {
  min-width: 1px;
  min-height: 1px;
  -gtk-icon-source: none;
  border-style: none;
  background-color: transparent;
  background-image: image(rgba(0, 0, 0, 0.12));
  background-size: 1px 1px;
  background-clip: content-box;
}

paned > separator.wide {
  min-width: 6px;
  min-height: 6px;
  background-color: #FFFFFF;
  background-image: image(rgba(0, 0, 0, 0.12)), image(rgba(0, 0, 0, 0.12));
  background-size: 1px 1px, 1px 1px;
}

paned.horizontal > separator {
  background-repeat: repeat-y;
}

paned.horizontal > separator:dir(ltr) {
  margin: 0 -8px 0 0;
  padding: 0 8px 0 0;
  background-position: left;
}

paned.horizontal > separator:dir(rtl) {
  margin: 0 0 0 -8px;
  padding: 0 0 0 8px;
  background-position: right;
}

paned.horizontal > separator.wide {
  margin: 0;
  padding: 0;
  background-repeat: repeat-y, repeat-y;
  background-position: left, right;
}

paned.vertical > separator {
  margin: 0 0 -8px 0;
  padding: 0 0 8px 0;
  background-repeat: repeat-x;
  background-position: top;
}

paned.vertical > separator.wide {
  margin: 0;
  padding: 0;
  background-repeat: repeat-x, repeat-x;
  background-position: bottom, top;
}

/**************
 * GtkInfoBar *
 **************/
infobar {
  border: none;
  margin-bottom: 0;
}

infobar.info > revealer > box, infobar.info:hover > revealer > box, infobar.info:backdrop > revealer > box {
  background-color: #FFFFFF;
}

infobar.info > revealer > box, infobar.info > revealer > box link:link, infobar.info > revealer > box flowboxchild, infobar.info:hover > revealer > box, infobar.info:hover > revealer > box link:link, infobar.info:hover > revealer > box flowboxchild, infobar.info:backdrop > revealer > box, infobar.info:backdrop > revealer > box link:link, infobar.info:backdrop > revealer > box flowboxchild {
  color: @accent_color;
}

infobar.info > revealer > box button.text-button:not(:disabled):not(.suggested-action):not(.destructive-action), infobar.info:hover > revealer > box button.text-button:not(:disabled):not(.suggested-action):not(.destructive-action), infobar.info:backdrop > revealer > box button.text-button:not(:disabled):not(.suggested-action):not(.destructive-action) {
  color: @accent_color;
}

infobar.action > revealer > box, infobar.action:backdrop > revealer > box, infobar.question > revealer > box, infobar.question:backdrop > revealer > box {
  background-color: @accent_color;
}

infobar.action > revealer > box, infobar.action > revealer > box link:link, infobar.action > revealer > box flowboxchild, infobar.action:backdrop > revealer > box, infobar.action:backdrop > revealer > box link:link, infobar.action:backdrop > revealer > box flowboxchild, infobar.question > revealer > box, infobar.question > revealer > box link:link, infobar.question > revealer > box flowboxchild, infobar.question:backdrop > revealer > box, infobar.question:backdrop > revealer > box link:link, infobar.question:backdrop > revealer > box flowboxchild {
  color: #FFFFFF;
}

infobar.action > revealer > box button, infobar.action > revealer > box button:hover, infobar.action > revealer > box button:focus, infobar.action > revealer > box button:active, infobar.action > revealer > box button:checked, infobar.action > revealer > box button.text-button:not(:disabled), infobar.action:backdrop > revealer > box button, infobar.action:backdrop > revealer > box button:hover, infobar.action:backdrop > revealer > box button:focus, infobar.action:backdrop > revealer > box button:active, infobar.action:backdrop > revealer > box button:checked, infobar.action:backdrop > revealer > box button.text-button:not(:disabled), infobar.question > revealer > box button, infobar.question > revealer > box button:hover, infobar.question > revealer > box button:focus, infobar.question > revealer > box button:active, infobar.question > revealer > box button:checked, infobar.question > revealer > box button.text-button:not(:disabled), infobar.question:backdrop > revealer > box button, infobar.question:backdrop > revealer > box button:hover, infobar.question:backdrop > revealer > box button:focus, infobar.question:backdrop > revealer > box button:active, infobar.question:backdrop > revealer > box button:checked, infobar.question:backdrop > revealer > box button.text-button:not(:disabled) {
  color: #FFFFFF;
}

infobar.action:hover > revealer > box, infobar.question:hover > revealer > box {
  background-color: @accent_bg_color;
}

infobar.warning > revealer > box, infobar.warning:backdrop > revealer > box {
  background-color: #FBC02D;
}

infobar.warning > revealer > box, infobar.warning > revealer > box link:link, infobar.warning > revealer > box flowboxchild, infobar.warning:backdrop > revealer > box, infobar.warning:backdrop > revealer > box link:link, infobar.warning:backdrop > revealer > box flowboxchild {
  color: rgba(0, 0, 0, 0.87);
}

infobar.warning > revealer > box button, infobar.warning > revealer > box button:hover, infobar.warning > revealer > box button:focus, infobar.warning > revealer > box button:active, infobar.warning > revealer > box button:checked, infobar.warning > revealer > box button.text-button:not(:disabled), infobar.warning:backdrop > revealer > box button, infobar.warning:backdrop > revealer > box button:hover, infobar.warning:backdrop > revealer > box button:focus, infobar.warning:backdrop > revealer > box button:active, infobar.warning:backdrop > revealer > box button:checked, infobar.warning:backdrop > revealer > box button.text-button:not(:disabled) {
  color: rgba(0, 0, 0, 0.87);
}

infobar.warning:hover > revealer > box {
  background-color: #fbc846;
}

infobar.error > revealer > box, infobar.error:backdrop > revealer > box {
  background-color: #E53935;
  color: #FFFFFF;
}

infobar.error > revealer > box, infobar.error > revealer > box link:link, infobar.error > revealer > box flowboxchild, infobar.error:backdrop > revealer > box, infobar.error:backdrop > revealer > box link:link, infobar.error:backdrop > revealer > box flowboxchild {
  color: #FFFFFF;
}

infobar.error > revealer > box button, infobar.error > revealer > box button:hover, infobar.error > revealer > box button:focus, infobar.error > revealer > box button:active, infobar.error > revealer > box button:checked, infobar.error > revealer > box button.text-button:not(:disabled), infobar.error:backdrop > revealer > box button, infobar.error:backdrop > revealer > box button:hover, infobar.error:backdrop > revealer > box button:focus, infobar.error:backdrop > revealer > box button:active, infobar.error:backdrop > revealer > box button:checked, infobar.error:backdrop > revealer > box button.text-button:not(:disabled) {
  color: #FFFFFF;
}

infobar.error:hover > revealer > box {
  background-color: #e84f4c;
}

/************
 * Tooltips *
 ************/
tooltip {
  box-shadow: none;
}

tooltip.background {
  background-color: rgba(52, 52, 52, 0.9);
  color: #FFFFFF;
  border-radius: 6px;
}

tooltip.background.csd {
  border-radius: 6px;
  box-shadow: 0 3px 3px -2px rgba(0, 0, 0, 0.05), 0 2px 3px -1px rgba(0, 0, 0, 0.06), 0 1px 4px 0 rgba(0, 0, 0, 0.05);
}

tooltip decoration {
  background-color: transparent;
}

tooltip > box {
  margin: -6px;
  min-height: 24px;
  padding: 4px 8px;
}

/*****************
 * Color Chooser *
 *****************/
colorswatch.top {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

colorswatch.top overlay {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

colorswatch.bottom {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

colorswatch.bottom overlay {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

colorswatch.left, colorswatch:first-child:not(.top) {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

colorswatch.left overlay, colorswatch:first-child:not(.top) overlay {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

colorswatch.right, colorswatch:last-child:not(.bottom) {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

colorswatch.right overlay, colorswatch:last-child:not(.bottom) overlay {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

colorswatch.dark {
  color: #FFFFFF;
}

colorswatch.light {
  color: rgba(0, 0, 0, 0.87);
}

colorswatch overlay {
  transition: box-shadow 200ms ease-out;
}

colorswatch overlay:hover {
  box-shadow: 0 0 0 2px @accent_color;
}

colorswatch#add-color-button {
  border-radius: 6px 0 0 6px;
}

colorswatch#add-color-button:only-child {
  border-radius: 6px;
}

colorswatch#add-color-button overlay {
  background-color: rgba(0, 0, 0, 0.04);
}

colorswatch#add-color-button overlay:hover {
  background-color: rgba(0, 0, 0, 0.12);
  box-shadow: none;
}

colorswatch#add-color-button overlay:active {
  background-color: rgba(0, 0, 0, 0.26);
}

colorswatch:disabled {
  opacity: 0.5;
}

colorswatch:disabled overlay {
  box-shadow: none;
}

colorswatch#editor-color-sample {
  border-radius: 12px;
}

colorswatch#editor-color-sample overlay {
  border-radius: 12px;
}

colorswatch#editor-color-sample overlay:hover {
  box-shadow: 0 2px 3px -2px rgba(0, 0, 0, 0.3), 0 1px 2px -1px rgba(0, 0, 0, 0.24), 0 1px 2px -1px rgba(0, 0, 0, 0.17);
}

colorchooser .popover.osd {
  transition: box-shadow 200ms ease-out;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15), 0 3px 3px 0 rgba(0, 0, 0, 0.18), 0 3px 6px 0 rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.15);
  background-color: #FFFFFF;
}

colorchooser .popover.osd:backdrop {
  box-shadow: 0 4px 3px -3px rgba(0, 0, 0, 0.2), 0 2px 2px -1px rgba(0, 0, 0, 0.24), 0 1px 3px 0 rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.15);
}

/********
 * Misc *
 ********/
.content-view {
  background-color: #FFFFFF;
}

/**********************
 * Window Decorations *
 **********************/
decoration {
  transition: none;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 15px 16px 2px rgba(0, 0, 0, 0.14), 0 6px 18px 5px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.12), 0 0 36px transparent;
  margin: 10px;
}

decoration:backdrop {
  transition: box-shadow 200ms ease-out;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15), 0 3px 3px 0 rgba(0, 0, 0, 0.18), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.12), 0 0 36px transparent;
}

.tiled decoration, .tiled-top decoration, .tiled-right decoration, .tiled-bottom decoration, .tiled-left decoration {
  border-radius: 0;
}

.maximized decoration, .fullscreen decoration {
  border-radius: 0;
  box-shadow: none;
}

.popup decoration {
  box-shadow: none;
  border: none;
}

.ssd decoration {
  border: none;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.12);
}

.metacity decoration {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border: none;
}

.csd.popup decoration {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.05), 0 4px 6px 0 rgba(0, 0, 0, 0.06), 0 1px 10px 0 rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.12);
}

tooltip.csd decoration {
  border-radius: 6px;
  box-shadow: none;
  border: none;
}

messagedialog.background.csd decoration {
  border-radius: 12px;
}

.solid-csd decoration {
  margin: 0;
  padding: 2px;
  border-radius: 0;
  box-shadow: none;
  background-color: #242424;
  border: 1px solid #e0e0e0;
}

.solid-csd decoration:backdrop {
  background-color: #2C2C2C;
}

button.titlebutton:not(.suggested-action):not(.destructive-action) {
  min-height: 22px;
  min-width: 22px;
  padding: 0;
  margin: 0 4px;
}

button.minimize.titlebutton:not(.suggested-action):not(.destructive-action), button.maximize.titlebutton:not(.suggested-action):not(.destructive-action), button.close.titlebutton:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.7);
  background-color: alpha(currentColor, 0.1);
}

button.minimize.titlebutton:hover:not(.suggested-action):not(.destructive-action), button.maximize.titlebutton:hover:not(.suggested-action):not(.destructive-action), button.close.titlebutton:hover:not(.suggested-action):not(.destructive-action) {
  color: #FFFFFF;
  background-color: alpha(currentColor, 0.15);
}

button.minimize.titlebutton:active:not(.suggested-action):not(.destructive-action), button.maximize.titlebutton:active:not(.suggested-action):not(.destructive-action), button.close.titlebutton:active:not(.suggested-action):not(.destructive-action) {
  color: #FFFFFF;
  background-color: alpha(currentColor, 0.2);
}

button.minimize.titlebutton:backdrop:not(.suggested-action):not(.destructive-action), button.maximize.titlebutton:backdrop:not(.suggested-action):not(.destructive-action), button.close.titlebutton:backdrop:not(.suggested-action):not(.destructive-action) {
  opacity: 0.65;
}

#MozillaGtkWidget.background headerbar.titlebar.default-decoration button.minimize.titlebutton, #MozillaGtkWidget.background headerbar.titlebar.default-decoration button.maximize.titlebutton, #MozillaGtkWidget.background headerbar.titlebar.default-decoration button.close.titlebutton {
  background-repeat: no-repeat;
  background-size: 16px 16px;
  background-position: center;
}

#MozillaGtkWidget.background headerbar.titlebar.default-decoration button.minimize.titlebutton, #MozillaGtkWidget.background headerbar.titlebar.default-decoration button.maximize.titlebutton, #MozillaGtkWidget.background headerbar.titlebar.default-decoration button.close.titlebutton {
  box-shadow: none;
  color: transparent;
  animation: none;
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
}

#MozillaGtkWidget.background headerbar.titlebar.default-decoration button.minimize.titlebutton:hover, #MozillaGtkWidget.background headerbar.titlebar.default-decoration button.minimize.titlebutton:active {
  background-image: -gtk-scaled(url("assets/minimize-symbolic.svg"), url("assets/<EMAIL>"));
}

#MozillaGtkWidget.background headerbar.titlebar.default-decoration button.maximize.titlebutton:hover, #MozillaGtkWidget.background headerbar.titlebar.default-decoration button.maximize.titlebutton:active {
  background-image: -gtk-scaled(url("assets/maximize-symbolic.svg"), url("assets/<EMAIL>"));
}

#MozillaGtkWidget.background headerbar.titlebar.default-decoration button.close.titlebutton:hover, #MozillaGtkWidget.background headerbar.titlebar.default-decoration button.close.titlebutton:active {
  background-image: -gtk-scaled(url("assets/close-symbolic.svg"), url("assets/<EMAIL>"));
}

.background.csd.maximized headerbar.titlebar.default-decoration button.titlebutton:not(.suggested-action):not(.destructive-action).maximize:hover, .background.csd.maximized headerbar.titlebar.default-decoration button.titlebutton:not(.suggested-action):not(.destructive-action).maximize:active {
  background-image: -gtk-scaled(url("assets/unmaximize-symbolic.svg"), url("assets/<EMAIL>"));
}

.monospace {
  font-family: monospace;
}

/**********************
 * Touch Copy & Paste *
 **********************/
cursor-handle {
  color: @accent_color;
  -gtk-icon-source: -gtk-recolor(url("assets/cursor-handle-symbolic.svg"));
}

cursor-handle.top:dir(ltr), cursor-handle.bottom:dir(rtl) {
  -gtk-icon-transform: rotate(90deg);
}

cursor-handle.bottom:dir(ltr), cursor-handle.top:dir(rtl) {
  -gtk-icon-transform: unset;
}

cursor-handle.insertion-cursor:dir(ltr), cursor-handle.insertion-cursor:dir(rtl) {
  padding-top: 6px;
  -gtk-icon-transform: rotate(45deg);
}

.context-menu {
  font: initial;
}

.keycap {
  min-width: 12px;
  min-height: 26px;
  margin-top: 2px;
  padding-bottom: 2px;
  padding-left: 8px;
  padding-right: 8px;
  border: solid 1px rgba(0, 0, 0, 0.12);
  border-radius: 7px;
  box-shadow: inset 0 -2px rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
  color: rgba(0, 0, 0, 0.87);
  font-size: smaller;
}

:not(decoration):not(window):drop(active) {
  caret-color: @accent_color;
}

stackswitcher {
  min-height: 0;
  padding: 3px;
  border-radius: 9px;
  background-color: rgba(0, 0, 0, 0.04);
  border: none;
}

stackswitcher.linked:not(.vertical) > button:not(.suggested-action):not(.destructive-action) {
  margin: 0;
  background-color: transparent;
  border-radius: 6px;
  padding: 2px 9px;
}

stackswitcher.linked:not(.vertical) > button:not(.suggested-action):not(.destructive-action) + button {
  margin-left: 3px;
}

stackswitcher.linked:not(.vertical) > button:not(.suggested-action):not(.destructive-action).text-button {
  min-width: 100px;
}

stackswitcher.linked:not(.vertical) > button:not(.suggested-action):not(.destructive-action):focus:not(:hover):not(:checked) {
  box-shadow: none;
}

stackswitcher.linked:not(.vertical) > button:not(.suggested-action):not(.destructive-action):checked {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  background-color: white;
  color: rgba(0, 0, 0, 0.87);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
}

stackswitcher button.text-button {
  min-width: 100px;
}

stackswitcher button.circular,
stackswitcher button.text-button.circular {
  min-width: 34px;
  min-height: 34px;
  padding: 0;
}

/*********
 * Emoji *
 *********/
popover.emoji-picker {
  padding: 0;
}

popover.emoji-picker.background entry {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  border-image: none;
  border-radius: 0;
  box-shadow: none;
  background-color: transparent;
}

popover.emoji-picker.background entry:focus {
  border-bottom: 1px solid @accent_color;
  box-shadow: inset 0 -1px @accent_color;
  background-color: transparent;
}

popover.emoji-picker scrolledwindow {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

button.emoji-section {
  margin: 4px;
}

button.emoji-section:checked {
  color: @accent_color;
}

button.emoji-section:not(:last-child) {
  margin-right: 0;
}

popover.emoji-picker .emoji {
  min-width: 3em;
  min-height: 3em;
  padding: 0 8px;
}

popover.emoji-picker .emoji widget {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  border-radius: 6px;
}

popover.emoji-picker .emoji widget:hover {
  background-color: alpha(currentColor, 0.08);
}

popover.emoji-completion {
  padding: 8px 0;
}

popover.emoji-completion arrow {
  border: none;
  background: none;
}

popover.emoji-completion .emoji-completion-row {
  min-height: 28px;
  padding: 0 12px;
}

popover.emoji-completion .emoji:hover {
  background-color: alpha(currentColor, 0.08);
}

/************
 * Nautilus *
 ************/
.nautilus-window.background.csd {
  border-radius: 0 0 12px 12px;
  background-color: #FAFAFA;
}

.nautilus-window.background.csd:backdrop {
  background-color: #FFFFFF;
}

.nautilus-window.background.csd > grid.horizontal > paned.horizontal > separator,
.nautilus-window.background.csd > deck > box.vertical > paned.horizontal > separator {
  margin-left: -1px;
}

.nautilus-window.background.csd placessidebar > viewport.frame > list > separator {
  background: none;
}

.nautilus-window.background.csd.unified notebook {
  border-radius: 0;
}

.nautilus-window.background.csd:not(.unified) .nautilus-list-view {
  background-color: transparent;
  border-bottom-right-radius: 12px;
}

.nautilus-window.background.csd:not(.unified) notebook {
  background-color: #FFFFFF;
  border-radius: 0 0 12px 12px;
}

.nautilus-window.background.csd:not(.unified) notebook > stack {
  background-color: transparent;
}

.nautilus-window.background.csd:not(.unified) notebook scrolledwindow > .view:not(:selected):not(:hover):not(:checked) {
  background-color: transparent;
}

.nautilus-window.background.csd:not(.unified) notebook placesview > stack > frame > scrolledwindow > viewport > list {
  background-color: transparent;
}

.nautilus-window.background.csd:not(.unified) notebook placesview > actionbar {
  background-color: transparent;
  border-radius: 0 0 12px 12px;
}

.nautilus-window.background.csd:not(.unified) notebook placesview > actionbar > revealer > box {
  background-color: transparent;
}

.nautilus-window.background.csd:not(.unified) paned > separator.wide {
  min-width: 12px;
  box-shadow: 12px 0 #FFFFFF;
  background-color: #FFFFFF;
  background-image: image(gainsboro);
}

.nautilus-window.maximized, .nautilus-window.maximized placessidebar {
  border-radius: 0;
}

.nautilus-window .floating-bar {
  margin: 6px;
  padding: 2px;
  box-shadow: 0 3px 3px -2px rgba(0, 0, 0, 0.05), 0 2px 3px -1px rgba(0, 0, 0, 0.06), 0 1px 4px 0 rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  background-color: @accent_color;
  color: #FFFFFF;
  border: none;
}

.nautilus-window .floating-bar button {
  min-height: 16px;
  min-width: 16px;
  padding: 2px;
  margin: 2px;
}

.floating-bar {
  min-height: 28px;
  padding: 0;
  border: 1px solid #e0e0e0;
  border-radius: 0;
  background-color: #FFFFFF;
  color: rgba(0, 0, 0, 0.87);
}

.floating-bar.bottom.right {
  border-top-left-radius: 6px;
  border-right: none;
  border-bottom: none;
}

.floating-bar.bottom.left {
  border-top-right-radius: 6px;
  border-left: none;
  border-bottom: none;
}

.floating-bar button {
  min-height: 16px;
  min-width: 16px;
  padding: 0;
  margin: 6px;
  border-radius: 9999px;
}

.nautilus-canvas-item.dim-label,
.nautilus-list-dim-label {
  color: rgba(0, 0, 0, 0.6);
}

.nemo-desktop.nemo-canvas-item, .caja-desktop.caja-canvas-item,
.nautilus-desktop.nautilus-canvas-item {
  color: #FFFFFF;
}

@keyframes nautilus-operations-button-needs-attention {
  to {
    background-color: alpha(currentColor, 0.08);
  }
}

.nautilus-operations-button-needs-attention {
  animation: nautilus-operations-button-needs-attention 300ms cubic-bezier(0.4, 0, 0.2, 1) 2 alternate;
}

.nautilus-operations-button-needs-attention-multiple {
  animation: nautilus-operations-button-needs-attention 300ms cubic-bezier(0.4, 0, 0.2, 1) 6 alternate;
}

.path-bar-box {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  margin: 6px 0;
  border-radius: 6px;
}

.path-bar-box button {
  margin: 0;
}

.path-bar-box.width-maximized {
  background-color: rgba(255, 255, 255, 0.04);
}

.path-bar-box.background.frame {
  border-style: none;
  background-color: rgba(255, 255, 255, 0.04);
}

.path-bar-box .path-bar button label:not(:only-child):first-child {
  margin-left: 0;
}

.path-bar-box .path-bar button label:not(:only-child):last-child {
  margin-right: 0;
}

.path-bar-box .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action) {
  padding-left: 11px;
  padding-right: 11px;
  margin-left: 1px;
  margin-right: 1px;
}

.path-bar-box .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action) label:not(:only-child):first-child {
  margin-left: 0;
}

.path-bar-box .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action) label:not(:only-child):last-child {
  margin-right: 0;
}

.path-bar-box .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action).text-button {
  min-width: 0;
}

.path-bar-box .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action).text-button.image-button image:not(:only-child) {
  margin: 0;
}

.path-bar-box .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action):last-child:dir(ltr), .path-bar-box .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action):first-child:dir(rtl) {
  background-color: rgba(255, 255, 255, 0.08);
}

.path-bar-box .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action):last-child:dir(ltr):disabled, .path-bar-box .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action):first-child:dir(rtl):disabled {
  background-color: transparent;
  color: rgba(255, 255, 255, 0.5);
}

.windowhandle .linked.nautilus-path-bar {
  background-color: rgba(255, 255, 255, 0.04);
  border-radius: 6px;
  margin: 6px 0;
}

.windowhandle .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action) {
  margin-top: 0;
  margin-bottom: 0;
}

.windowhandle .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action).current-dir {
  color: #FFFFFF;
}

.windowhandle .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action).current-dir:hover, .windowhandle .linked.nautilus-path-bar button:not(.suggested-action):not(.destructive-action).current-dir:active {
  background: none;
  box-shadow: none;
}

.disk-space-display.unknown {
  background-color: rgba(0, 0, 0, 0.26);
  color: rgba(0, 0, 0, 0.26);
}

.disk-space-display.used {
  background-color: @accent_color;
  color: @accent_color;
}

.disk-space-display.free {
  background-color: rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.12);
}

.search-information {
  padding: 2px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
  color: rgba(0, 0, 0, 0.87);
}

.conflict-row:not(:selected) {
  background-color: #feecc0;
}

.nautilus-window flowboxchild .icon-item-background {
  padding: 4px;
  border-radius: 6px;
}

.nautilus-window flowboxchild:selected {
  background-color: transparent;
}

.nautilus-window notebook :not(treeview).view {
  border-radius: 6px;
}

dialog.background > box.dialog-vbox.vertical > grid.horizontal > scrolledwindow.frame {
  border-style: none;
}

dialog.background > box.dialog-vbox.vertical > grid.horizontal > box.horizontal:last-child {
  margin: -6px 0 0 -6px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

dialog.background > box.dialog-vbox.vertical > grid.horizontal > box.horizontal:last-child > label {
  margin: 0 8px;
}

dialog.background > box.dialog-vbox.vertical > grid.horizontal > box.horizontal:last-child > box > button {
  border-radius: 0;
}

.nautilus-window > popover.menu:not(:last-child) {
  padding: 3px;
}

.nautilus-window > popover.menu:not(:last-child) > stack > box > box > box {
  margin-top: -6px;
}

.nautilus-window > popover.menu:not(:last-child) > stack > box > box > box > box {
  margin-bottom: -6px;
}

.nautilus-window > popover.menu:not(:last-child) > stack > box > box > box > box.linked {
  margin-top: 1px;
}

.nautilus-window > popover.menu:not(:last-child) separator {
  margin-bottom: -2px;
}

.nautilus-menu-sort-heading {
  margin: 1px 3px;
  font-weight: 500;
}

.nautilus-menu-sort-heading:disabled {
  color: rgba(0, 0, 0, 0.6);
}

.nautilus-window paned > separator {
  background-color: #FAFAFA;
}

/*********
 * gedit *
 *********/
window.org-gnome-gedit > paned.titlebar > separator {
  background-color: transparent;
}

window.org-gnome-gedit > overlay > box.vertical > paned.gedit-side-panel-paned > box.vertical > stack > grid.horizontal > box.horizontal {
  margin: 4px 0;
}

window.org-gnome-gedit > overlay > box.vertical > paned.gedit-side-panel-paned > box.vertical > stack > grid.horizontal > scrolledwindow {
  border-bottom-left-radius: 12px;
}

window.org-gnome-gedit > overlay > box.vertical > paned.gedit-side-panel-paned stack scrolledwindow viewport.frame list.gedit-document-panel {
  background: none;
}

.open-document-selector-path-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: smaller;
}

.open-document-selector-match {
  background-color: #FBC02D;
  color: rgba(0, 0, 0, 0.87);
}

.gedit-document-panel {
  background-color: #FAFAFA;
}

.gedit-document-panel row button.flat {
  margin-top: 8px;
  margin-bottom: 8px;
}

.gedit-document-panel-group-row:not(:first-child) {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.gedit-side-panel-paned statusbar {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 0 0 12px 12px;
}

.gedit-search-slider {
  margin: 0 6px 10px;
  padding: 6px;
  background-color: #FFFFFF;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.05), 0 4px 6px 0 rgba(0, 0, 0, 0.06), 0 1px 10px 0 rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.12);
}

.gedit-search-slider .linked:not(.vertical) > entry .gedit-search-entry-occurrences-tag {
  all: unset;
  color: rgba(0, 0, 0, 0.6);
}

.gedit-search-slider .linked:not(.vertical) > entry:dir(ltr) .gedit-search-entry-occurrences-tag {
  margin-left: 6px;
}

.gedit-search-slider .linked:not(.vertical) > entry:dir(ltr) image.right {
  margin-right: 0;
}

.gedit-search-slider .linked:not(.vertical) > entry:dir(rtl) .gedit-search-entry-occurrences-tag {
  margin-right: 6px;
}

.gedit-search-slider .linked:not(.vertical) > entry:dir(rtl) image.left {
  margin-left: 0;
}

.gedit-search-slider .linked:not(.vertical) > entry:not(.error) {
  background-color: #FFFFFF;
}

.gedit-search-slider .linked:not(.vertical) > entry.error ~ button {
  color: rgba(255, 255, 255, 0.7);
}

.gedit-search-slider .linked:not(.vertical) > entry.error ~ button:hover, .gedit-search-slider .linked:not(.vertical) > entry.error ~ button:active {
  color: #FFFFFF;
}

.gedit-search-slider .linked:not(.vertical) > entry.error ~ button:disabled {
  color: rgba(255, 255, 255, 0.32);
}

frame.gedit-map-frame > border:dir(ltr) {
  border-style: none none none solid;
}

frame.gedit-map-frame > border:dir(rtl) {
  border-style: none solid none none;
}

/**********
 * Tweaks *
 **********/
.tweak-categories > row + row {
  margin-top: 3px;
}

.csd .tweak-categories {
  border-bottom-left-radius: 12px;
}

leaflet list.navigation-sidebar {
  background-color: #FAFAFA;
}

leaflet list.navigation-sidebar > row + row {
  margin-top: 3px;
}

window.background.csd:not(.maximized) leaflet list.navigation-sidebar {
  border-bottom-left-radius: 12px;
}

.tweak-group-white,
.tweak-white,
.tweak-white:hover {
  background-image: image(#FFFFFF);
}

.tweak-group-startup {
  padding: 0;
}

.tweak-group-startup > row.tweak-startup {
  background-color: transparent;
  background-image: none;
}

list.tweak-group list, list#ListBoxTweakGroup list {
  padding: 0;
}

row#Focus,
row#ClickMethod,
row#PrimaryWorkspaceTweak,
row#workspaces-only-on-primary {
  margin-top: 4px;
}

leaflet.titlebar > .titlebar.tweak-titlebar-left,
leaflet.titlebar > .titlebar.tweak-titlebar-right,
hdyleaflet.titlebar > .titlebar.tweak-titlebar-left,
hdyleaflet.titlebar > .titlebar.tweak-titlebar-right {
  background-color: inherit;
  box-shadow: inherit;
  transition: color 75ms cubic-bezier(0, 0, 0.2, 1);
}

/************************
 * Gnome Control Center *
 ************************/
window.background.csd > headerbar.titlebar > leaflet > headerbar:first-child:not(:only-child),
window.background.csd > headerbar.titlebar > hdyleaflet > headerbar:first-child:not(:only-child) {
  border-top-left-radius: 12px;
}

window.background.csd > headerbar.titlebar > leaflet > headerbar:last-child:not(:only-child),
window.background.csd > headerbar.titlebar > hdyleaflet > headerbar:last-child:not(:only-child) {
  border-top-right-radius: 12px;
}

window.background.csd > headerbar.titlebar > leaflet > headerbar:first-child:only-child, window.background.csd > headerbar.titlebar > leaflet > headerbar:last-child:only-child,
window.background.csd > headerbar.titlebar > hdyleaflet > headerbar:first-child:only-child,
window.background.csd > headerbar.titlebar > hdyleaflet > headerbar:last-child:only-child {
  border-top-right-radius: 12px;
  border-top-left-radius: 12px;
}

window.background.csd > stack:not(.titlebar) > stack.background {
  border-radius: 0 0 12px 12px;
}

window.background.csd > leaflet > stack.background,
window.background.csd > hdyleaflet > stack.background,
window.background.csd > box.horizontal > stack.background {
  background: none;
}

window.background.csd > leaflet > stack.background frame > border,
window.background.csd > hdyleaflet > stack.background frame > border,
window.background.csd > box.horizontal > stack.background frame > border {
  border: none;
}

window.background.csd > leaflet > stack.background > widget > box.vertical > box.vertical > scrolledwindow > viewport.frame,
window.background.csd > hdyleaflet > stack.background > widget > box.vertical > box.vertical > scrolledwindow > viewport.frame,
window.background.csd > box.horizontal > stack.background > widget > box.vertical > box.vertical > scrolledwindow > viewport.frame {
  background-color: #FFFFFF;
  border-bottom-right-radius: 12px;
}

window.background.csd > leaflet > stack.background > widget > box.vertical > box.vertical > scrolledwindow > viewport.frame > box.vertical.view,
window.background.csd > hdyleaflet > stack.background > widget > box.vertical > box.vertical > scrolledwindow > viewport.frame > box.vertical.view,
window.background.csd > box.horizontal > stack.background > widget > box.vertical > box.vertical > scrolledwindow > viewport.frame > box.vertical.view {
  background: none;
}

window.background.csd > leaflet frame.view,
window.background.csd > hdyleaflet frame.view,
window.background.csd > box.horizontal frame.view {
  border-radius: 6px;
  background: none;
}

window.background.csd > leaflet > box.vertical > scrolledwindow.view,
window.background.csd > hdyleaflet > box.vertical > scrolledwindow.view,
window.background.csd > box.horizontal > box.vertical > scrolledwindow.view {
  background-color: #FFFFFF;
  border-bottom-left-radius: 12px;
}

window.background.csd > leaflet > box.vertical > scrolledwindow.view > viewport.frame > stack,
window.background.csd > hdyleaflet > box.vertical > scrolledwindow.view > viewport.frame > stack,
window.background.csd > box.horizontal > box.vertical > scrolledwindow.view > viewport.frame > stack {
  background-color: transparent;
}

window.background.csd > leaflet > box.vertical > scrolledwindow.view > viewport.frame > stack list,
window.background.csd > hdyleaflet > box.vertical > scrolledwindow.view > viewport.frame > stack list,
window.background.csd > box.horizontal > box.vertical > scrolledwindow.view > viewport.frame > stack list {
  background-color: transparent;
  padding: 6px;
}

window.background.csd > leaflet > box.vertical > scrolledwindow.view > viewport.frame > stack list > row.activatable,
window.background.csd > hdyleaflet > box.vertical > scrolledwindow.view > viewport.frame > stack list > row.activatable,
window.background.csd > box.horizontal > box.vertical > scrolledwindow.view > viewport.frame > stack list > row.activatable {
  border-radius: 6px;
}

window.background.csd > leaflet > box.vertical > scrolledwindow.view > viewport.frame > stack list > row.activatable + row,
window.background.csd > hdyleaflet > box.vertical > scrolledwindow.view > viewport.frame > stack list > row.activatable + row,
window.background.csd > box.horizontal > box.vertical > scrolledwindow.view > viewport.frame > stack list > row.activatable + row {
  margin-top: 3px;
}

window.background.csd > leaflet > box.vertical > scrolledwindow.view > viewport.frame > stack list > row.activatable:not(:hover):not(:active):not(:selected),
window.background.csd > hdyleaflet > box.vertical > scrolledwindow.view > viewport.frame > stack list > row.activatable:not(:hover):not(:active):not(:selected),
window.background.csd > box.horizontal > box.vertical > scrolledwindow.view > viewport.frame > stack list > row.activatable:not(:hover):not(:active):not(:selected) {
  background-color: transparent;
}

window.background.csd stack.background clamp.medium frame > box.vertical > box.vertical > list {
  border-top-width: 0;
  border-bottom-width: 0;
}

window.background.csd stack.background clamp.medium frame > box.vertical > box.vertical > list, window.background.csd stack.background clamp.medium frame > box.vertical > box.vertical > list > row {
  border-radius: 0;
}

window.background.csd stack.background clamp.medium frame > box.vertical > box.vertical:first-child > list {
  border-top-width: 1px;
}

window.background.csd stack.background clamp.medium frame > box.vertical > box.vertical:first-child > list, window.background.csd stack.background clamp.medium frame > box.vertical > box.vertical:first-child > list > row {
  border-radius: 6px 6px 0 0;
}

window.background.csd stack.background clamp.medium frame > box.vertical > box.vertical:last-child > list {
  border-bottom-width: 1px;
}

window.background.csd stack.background clamp.medium frame > box.vertical > box.vertical:last-child > list, window.background.csd stack.background clamp.medium frame > box.vertical > box.vertical:last-child > list > row {
  border-radius: 0 0 6px 6px;
}

window.background.csd hdycolumn stack frame.view > stack > stack list > separator {
  background-color: rgba(0, 0, 0, 0.12);
}

dialog.background.csd > box.vertical.dialog-vbox > stack > notebook,
dialog.background.csd > box.vertical.dialog-vbox > notebook > stack > box.horizontal > notebook > stack {
  border-radius: 0 0 12px 12px;
}

dialog.background.csd > box.vertical.dialog-vbox > scrolledwindow > viewport.frame > list {
  background: none;
}

dialog.background.csd > box.vertical.dialog-vbox > scrolledwindow > viewport.frame > list > row:not(:hover):not(:active):not(:selected) {
  background-color: transparent;
}

hdyleaflet > box.vertical > scrolledwindow > viewport.frame list:not(.view):not(.tweak-group):not(.tweak-group-startup), leaflet > box.vertical > scrolledwindow > viewport.frame list:not(.view):not(.tweak-group):not(.tweak-group-startup), box.horizontal > stack.background > box.vertical > scrolledwindow > viewport.frame list:not(.view):not(.tweak-group):not(.tweak-group-startup) {
  padding: 3px;
}

hdyleaflet > box.vertical > scrolledwindow > viewport.frame list:not(.view):not(.tweak-group):not(.tweak-group-startup) row.activatable, leaflet > box.vertical > scrolledwindow > viewport.frame list:not(.view):not(.tweak-group):not(.tweak-group-startup) row.activatable, box.horizontal > stack.background > box.vertical > scrolledwindow > viewport.frame list:not(.view):not(.tweak-group):not(.tweak-group-startup) row.activatable {
  border-radius: 6px;
}

/************************
 * Gnome system monitor *
 ************************/
window#gnome-system-monitor.background.csd > box.vertical > stack {
  background-color: #FFFFFF;
  border-radius: 0 0 12px 12px;
}

window#gnome-system-monitor.background.csd > box.vertical > stack > box.vertical > revealer > actionbar > revealer > box {
  border-radius: 0 0 12px 12px;
}

window#gnome-system-monitor.background:not(.csd) > box.vertical > headerbar {
  box-shadow: none;
}

/************************
 * Gnome Sound Recorder *
 ************************/
stack > grid.vertical > scrolledwindow {
  border: none;
  border-radius: 0 0 12px 12px;
}

stack > grid.vertical > scrolledwindow > viewport.frame list {
  border-radius: 0 0 12px 12px;
}

stack > grid.vertical scrolledwindow.frame.emptyGrid {
  border: none;
}

/******************
 * Gnome Contacts *
 ******************/
window.background.csd scrolledwindow.contacts-contact-form {
  border-bottom-right-radius: 12px;
}

/************************
 * Epiphany (Gnome Web) *
 ************************/
tabbox {
  border: none;
  background-color: rgba(0, 0, 0, 0.04);
  padding: 3px;
  margin: 3px;
  border-radius: 9px;
}

tabbox > tab button {
  min-height: 24px;
  min-width: 24px;
  border-radius: 9999px;
  border: none;
  padding: 0;
  margin-right: -6px;
}

/*****************
 * Gnome Weather *
 *****************/
#weather-page,
#weekly-forecast-frame {
  border-bottom-right-radius: 12px;
}

#weather-page-content-view {
  border-bottom-right-radius: 12px;
  border-bottom-left-radius: 12px;
}

/***************
 * Gnome Music *
 ***************/
window.background.csd box.vertical > overlay > stack.background {
  border-radius: 0 0 12px 12px;
}

/****************
 * Gnome Clocks *
 ****************/
/*************
 * Rhythmbox *
 *************/
window.background > box.vertical > toolbar.primary-toolbar > toolitem > box.horizontal:not(.linked) > button.toggle,
window.background > box.vertical > toolbar.primary-toolbar > toolitem > .linked > button:not(.toggle):not(.raised):not(.flat), window.background > box.vertical > toolbar.primary-toolbar > toolitem button.flat.scale, window.csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > .linked > button,
window.csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > box.horizontal > button,
window.solid-csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > .linked > button,
window.solid-csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > box.horizontal > button {
  min-height: 24px;
  min-width: 24px;
  padding: 5px;
  margin: 0;
}

.sidebar-paned .inline-toolbar.horizontal.sidebar-toolbar {
  box-shadow: inset 0 1px rgba(0, 0, 0, 0.12);
}

.sidebar-paned .inline-toolbar.horizontal.sidebar-toolbar button.image-button {
  border-radius: 9999px;
}

.sidebar-paned .inline-toolbar.horizontal.sidebar-toolbar button.image-button:not(:first-child), .sidebar-paned .inline-toolbar.horizontal.sidebar-toolbar button.image-button:not(:last-child) {
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}

.sidebar-paned .inline-toolbar.horizontal.sidebar-toolbar button.image-button.image-button:not(.text-button):first-child {
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}

.sidebar-paned .inline-toolbar.horizontal.sidebar-toolbar button.image-button.image-button:not(.text-button):last-child {
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}

.sidebar-paned .inline-toolbar.horizontal.sidebar-toolbar button.image-button > widget > box > image {
  padding: 0;
}

window.csd > box.vertical > box.vertical > toolbar.horizontal,
window.solid-csd > box.vertical > box.vertical > toolbar.horizontal {
  padding: 6px;
  margin: -1px 0;
  border-bottom: none;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  box-shadow: none;
  background-color: transparent;
}

window.csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > .linked > button,
window.csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > box.horizontal > button,
window.solid-csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > .linked > button,
window.solid-csd > box.vertical > box.vertical > toolbar.horizontal > toolitem > box.horizontal > button {
  margin: 6px 0;
}

window.csd > box.vertical > box.vertical > frame,
window.solid-csd > box.vertical > box.vertical > frame {
  margin: -1px 0;
  padding: 0;
}

window.csd > box.vertical > box.vertical > frame > border,
window.solid-csd > box.vertical > box.vertical > frame > border {
  border: none;
}

window.background > box.vertical > toolbar.primary-toolbar {
  padding: 0 12px 0 6px;
}

window.background > box.vertical > toolbar.primary-toolbar > toolitem > .linked > button.image-button.raised {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 300ms cubic-bezier(0, 0, 0.2, 1), background-image 1200ms cubic-bezier(0, 0, 0.2, 1);
  outline: none;
  box-shadow: inset 0 0 0 9999px transparent;
  background-color: rgba(0, 0, 0, 0.04);
  background-image: radial-gradient(circle, transparent 10%, transparent 0%);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1000% 1000%;
  color: rgba(0, 0, 0, 0.87);
}

window.background > box.vertical > toolbar.primary-toolbar > toolitem > .linked > button.image-button.raised:hover {
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.08);
}

window.background > box.vertical > toolbar.primary-toolbar > toolitem > .linked > button.image-button.raised:active {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms, border 0ms;
  animation: ripple 225ms cubic-bezier(0, 0, 0.2, 1) forwards;
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.08);
  background-image: radial-gradient(circle, alpha(currentColor, 0.12) 10%, transparent 0%);
  background-size: 0% 0%;
}

window.background > box.vertical > toolbar.primary-toolbar > toolitem > .linked > button.image-button.raised:disabled {
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.38);
}

window.background > box.vertical > toolbar.primary-toolbar > toolitem > .linked > button.image-button.raised:checked {
  background-color: @accent_color;
  color: #FFFFFF;
}

window.background > box.vertical > toolbar.primary-toolbar > toolitem > .linked > button.image-button.raised > widget > box > image {
  padding: 0 3px;
}

window.background > box > .sidebar-paned > paned > box > notebook > stack > box > grid > grid > grid,
window.background > box > .sidebar-paned > paned > box > notebook > stack > box > box > grid > grid > grid,
window.background > box > .sidebar-paned > paned > box > notebook > stack > box > notebook > stack > grid > grid > grid {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  padding: 6px;
  margin: -6px;
}

window.background > box > .sidebar-paned > paned > box > notebook > stack > box > grid > grid > box,
window.background > box > .sidebar-paned > paned > box > notebook > stack > box > box > grid > grid > box,
window.background > box > .sidebar-paned > paned > box > notebook > stack > box > notebook > stack > grid > grid > box {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  padding: 0 6px 6px;
  margin: 0 -6px -6px 0;
}

window.background > box > .sidebar-paned > paned > box > notebook > stack > box > grid > paned > box > scrolledwindow:not(:last-child),
window.background > box > .sidebar-paned > paned > box > notebook > stack > box > notebook > stack > grid > paned > box > scrolledwindow:not(:last-child) {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  margin-right: -1px;
}

window.background > box > .sidebar-paned > paned > box > notebook > stack > box > box > grid > grid > grid,
window.background > box > .sidebar-paned > paned > box > notebook > stack > box > box > box > grid > grid > grid,
window.background > box > .sidebar-paned > paned > box > notebook > stack > box > box > notebook > stack > grid > grid > grid {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  padding: 3px;
  margin: -1px -6px -6px;
}

window.background > box > .sidebar-paned > paned > box > notebook > stack > box > box > grid > paned > box > scrolledwindow:not(:last-child),
window.background > box > .sidebar-paned > paned > box > notebook > stack > box > box > box > grid > paned > box > scrolledwindow:not(:last-child),
window.background > box > .sidebar-paned > paned > box > notebook > stack > box > box > notebook > stack > grid > paned > box > scrolledwindow:not(:last-child) {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  margin-right: -1px;
}

window.background > box > .sidebar-paned > paned > box > notebook > stack > box > box > paned > box:first-child > box {
  padding: 0 6px 6px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

/**********
 * Polari *
 **********/
.polari-room-list .sidebar {
  background: none;
}

.polari-room-list .sidebar > row.activatable:selected {
  background-color: @accent_color;
  color: #FFFFFF;
}

stack.view.polari-entry-area {
  background-color: #FFFFFF;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  border-bottom-right-radius: 12px;
}

stack:disabled.view.polari-entry-area {
  background-image: image(#FFFFFF);
}

/***********
 * Builder *
 ***********/
layouttabbar {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
}

layouttabbar > box > button {
  margin: 3px 0;
}

layouttab {
  margin: 0 8px;
  border-style: none solid;
  border-width: 1px;
  border-color: rgba(0, 0, 0, 0.12);
  box-shadow: inset 0 -2px @accent_color;
  background-color: #FFFFFF;
}

layouttab separator.vertical {
  margin: 8px 4px;
}

layouttab button.text-button, layouttab button.image-button, layouttab button {
  margin-top: 8px;
  margin-bottom: 8px;
  padding: 0 4px;
}

layout {
  border: 1px solid rgba(0, 0, 0, 0.12);
  -PnlDockBin-handle-size: 1;
}

entry.search-missing {
  background-color: #E53935;
  color: #FFFFFF;
}

window.workbench treeview.image {
  color: rgba(0, 0, 0, 0.6);
}

popover.popover-selector list {
  padding: 6px;
}

popover.popover-selector list row {
  border-radius: 6px;
}

popover.popover-selector list row image:dir(ltr) {
  margin-right: 6px;
}

popover.popover-selector list row image:dir(rtl) {
  margin-left: 6px;
}

popover.popover-selector list row .accel:dir(ltr) {
  margin-left: 6px;
}

popover.popover-selector list row .accel:dir(rtl) {
  margin-right: 6px;
}

omnibar.linked:not(.vertical) entry {
  border-radius: 6px;
}

omnibar:not(:hover):not(:active) entry {
  color: rgba(0, 0, 0, 0.6);
}

popover.omnibar list row:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

entry.preferences-search {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  box-shadow: none;
  background-color: #FFFFFF;
}

preferences stacksidebar.sidebar list {
  background-color: #FAFAFA;
}

preferences stacksidebar.sidebar:dir(ltr) list, preferences stacksidebar.sidebar:dir(rtl) list {
  border-style: none;
}

preferences > box > box:dir(ltr) {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

preferences > box > box:dir(rtl) {
  border-left: 1px solid rgba(0, 0, 0, 0.12);
}

popover.messagepopover.background {
  padding: 0;
}

popover.messagepopover .popover-action-area button {
  padding: 8px 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 0;
}

popover.messagepopover .popover-action-area button:first-child {
  border-bottom-left-radius: 6px;
}

popover.messagepopover .popover-action-area button:last-child {
  border-bottom-right-radius: 6px;
}

popover.messagepopover .popover-content-area {
  margin: 16px;
}

popover.transfers list {
  background-color: transparent;
}

popover.transfers list row:not(:first-child) {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

popover.transfers list row > box {
  padding: 10px;
}

dockbin {
  border: 1px solid rgba(0, 0, 0, 0.12);
  -PnlDockBin-handle-size: 1;
}

dockpaned {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

eggsearchbar box.search-bar {
  padding: 0 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
}

docktabstrip {
  padding: 0 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
}

docktab {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 0ms, background-image 0ms;
  min-height: 24px;
  min-width: 24px;
  margin-bottom: -1px;
  padding: 6px 6px;
  border-width: 1px;
  border-color: transparent;
  box-shadow: inset 0 -2px transparent;
  background-image: radial-gradient(circle, @accent_color 10%, transparent 0%);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 0% 0%;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 500;
}

docktab:hover {
  background-color: alpha(currentColor, 0.08);
  color: rgba(0, 0, 0, 0.87);
}

docktab:checked {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1), background-size 225ms cubic-bezier(0, 0, 0.2, 1), background-image 525ms cubic-bezier(0, 0, 0.2, 1);
  box-shadow: inset 0 -2px @accent_color;
  background-color: transparent;
  background-image: radial-gradient(circle, transparent 10%, transparent 0%);
  background-size: 1000% 1000%;
  color: rgba(0, 0, 0, 0.87);
}

dockoverlayedge {
  background-color: #FFFFFF;
}

dockoverlayedge docktabstrip {
  padding: 0;
  border: none;
}

dockoverlayedge.left-edge docktab:checked {
  box-shadow: inset -2px 0 @accent_color;
}

dockoverlayedge.right-edge docktab:checked {
  box-shadow: inset 2px 0 @accent_color;
}

pillbox {
  background-color: #FFFFFF;
  border-radius: 6px;
}

layoutpane entry.search {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  box-shadow: none;
  background-color: #FFFFFF;
}

editortweak entry.search {
  margin-bottom: -1px;
  box-shadow: none;
  background-color: transparent;
}

.gb-search-entry-occurrences-tag {
  box-shadow: none;
  background-color: transparent;
}

docktabstrip {
  min-height: 39px;
}

window.workbench preferences preferencesgroup list entry {
  padding-top: 8px;
  padding-bottom: 8px;
}

button.run-arrow-button {
  padding-left: 9px;
  padding-right: 9px;
}

button.dzlmenubutton image {
  min-width: 28px;
}

button.dzlmenubutton image.arrow {
  min-width: 25px;
}

button.dzlmenubuttonitem {
  color: rgba(0, 0, 0, 0.87);
  font-weight: normal;
}

button.dzlmenubuttonitem:disabled {
  color: rgba(0, 0, 0, 0.38);
}

idelayoutstackheader {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

idelayoutstackheader button:checked {
  color: rgba(0, 0, 0, 0.87);
}

ideeditorutilities > dzldockpaned > box > stackswitcher {
  padding: 8px 0;
  background-color: #FFFFFF;
}

ideeditorutilities > dzldockpaned > box > stackswitcher:dir(ltr) {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

ideeditorutilities > dzldockpaned > box > stackswitcher:dir(rtl) {
  border-left: 1px solid rgba(0, 0, 0, 0.12);
}

ideeditorutilities > dzldockpaned > box > stackswitcher button {
  border-radius: 0;
  box-shadow: none;
  background-color: transparent;
}

ideeditorutilities > dzldockpaned > box > stackswitcher button:active {
  background-image: radial-gradient(circle, alpha(@accent_color, 0.7) 10%, transparent 0%);
}

ideeditorutilities > dzldockpaned > box > stackswitcher button:checked {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.87);
}

ideeditorutilities > dzldockpaned > box > stackswitcher button:dir(ltr) {
  margin-right: -1px;
}

ideeditorutilities > dzldockpaned > box > stackswitcher button:dir(ltr):checked {
  box-shadow: inset -2px 0 @accent_color;
}

ideeditorutilities > dzldockpaned > box > stackswitcher button:dir(rtl) {
  margin-left: -1px;
}

ideeditorutilities > dzldockpaned > box > stackswitcher button:dir(rtl):checked {
  box-shadow: inset 2px 0 @accent_color;
}

ideeditorsidebar notebook header {
  background: transparent;
}

popover.messagepopover list {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

popover.messagepopover list row:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

/**********
 * Photos *
 **********/
GdMainIconView.content-view {
  -GdMainIconView-icon-size: 48;
}

.documents-counter {
  margin: 8px;
  border-radius: 9999px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15), 0 3px 3px 0 rgba(0, 0, 0, 0.18), 0 3px 6px 0 rgba(0, 0, 0, 0.12);
  background-color: @accent_color;
  color: #FFFFFF;
  font-weight: bold;
}

.documents-scrolledwin.frame {
  border-style: none;
}

.documents-scrolledwin.frame frame.content-view > border {
  border-style: none;
}

.photos-fade-in {
  opacity: 1;
  transition: opacity 75ms cubic-bezier(0, 0, 0.2, 1);
}

.photos-fade-out {
  opacity: 0;
  transition: opacity 75ms cubic-bezier(0, 0, 0.2, 1);
}

button.photos-filter-preview {
  color: rgba(0, 0, 0, 0.87);
  font-weight: normal;
}

button.photos-filter-preview:checked {
  background-color: alpha(currentColor, 0.06);
  color: rgba(0, 0, 0, 0.87);
}

button.photos-filter-preview:checked image {
  color: #FFFFFF;
}

overlay grid.horizontal > revealer > scrolledwindow.frame:dir(ltr) {
  border-style: none none none solid;
}

overlay grid.horizontal > revealer > scrolledwindow.frame:dir(rtl) {
  border-style: none solid none none;
}

/*********
 * Music *
 *********/
.side-panel:dir(ltr) {
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.12);
}

.side-panel:dir(rtl) {
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.12);
}

.side-panel .view {
  background-image: image(#FAFAFA);
}

.side-panel .view:hover {
  background-image: image(rgba(234, 234, 234, 0.9896));
}

.side-panel .view:selected {
  background-image: image(@accent_color);
}

.side-panel .view:selected:hover {
  background-image: image(@accent_bg_color);
}

.songs-list:hover {
  background-image: image(alpha(currentColor, 0.08));
}

frame.documents-dropdown {
  margin: 8px;
}

frame.documents-dropdown > border {
  border: none;
}

box.vertical > revealer > toolbar.search-bar {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  background-clip: border-box;
}

box.vertical > revealer > toolbar.search-bar button > widget {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
}

/*************
 * Documents *
 *************/
.documents-scrolledwin {
  background-color: transparent;
}

.documents-scrolledwin .content-view:not(:selected):not(:hover) {
  background-color: transparent;
}

.documents-scrolledwin viewport.frame {
  background-color: transparent;
}

.documents-scrolledwin viewport.frame widget > frame.content-view:not(:selected):not(:hover) {
  background-color: transparent;
}

.documents-scrolledwin viewport.frame widget > frame.content-view:not(:selected):not(:hover) border {
  border: none;
}

window.background.csd > stack > box > revealer > actionbar > revealer > box {
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

/*******************
 * Document Viewer *
 *******************/
window.background.csd evview.view.content-view {
  border-radius: 0 0 12px 12px;
}

/*********************************
 * Archive Manager (File roller) *
 *********************************/
.background.csd > grid.horizontal > paned.horizontal > scrolledwindow {
  border-radius: 0 0 12px 12px;
  background-color: #FFFFFF;
}

.background.csd > grid.horizontal > paned.horizontal > scrolledwindow > treeview.view:not(:hover):not(:selected):not(:selected):not(:hover):not(.progressbar):not(.expander):not(.trough):not(.separator) {
  background-color: #FFFFFF;
}

.background.csd > grid.horizontal > paned.horizontal > box.vertical > scrolledwindow {
  border-radius: 0 0 0 12px;
  background-color: #FFFFFF;
}

/************
 * Terminal *
 ************/
terminal-window decoration {
  border-radius: 12px 12px 0 0;
}

terminal-window.background.csd, terminal-window.background.csd.maximized {
  border-radius: 0 0 0 0;
}

terminal-window notebook > header > box {
  margin: -2px -2px -2px 1px;
}

terminal-window notebook > header > box button {
  min-height: 24px;
  min-width: 24px;
  padding: 3px;
}

window.background > box.vertical > box.horizontal > frame > border {
  border-width: 0 1px 0 0;
}

window.background > box.vertical > box.horizontal > frame > scrolledwindow > viewport.frame list {
  border-bottom-left-radius: 12px;
}

window.background > box.vertical > box.horizontal > stack > widget > notebook.frame {
  border-width: 0;
  border-radius: 0 0 12px 0;
}

window.background > box.vertical > box.horizontal > stack > widget > notebook.frame > stack {
  border-bottom-right-radius: 12px;
}

.terminal-window {
  background-color: #FFFFFF;
}

.terminal-window tabbar tabbox {
  background-color: #FAFAFA;
  margin: 0;
  border-radius: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  background-clip: border-box;
}

/*********
 * To Do *
 *********/
task-list-view taskrow {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
  margin: 0 -8px;
}

task-list-view taskrow:hover {
  transition: none;
}

task-list-view taskrow label {
  margin: 0 8px;
}

task-list-view taskrow image.dim-label {
  min-width: 16px;
}

task-list-view > box > revealer > box > button {
  margin: -5px;
}

task-list-view > box > revealer > box > button .dim-label {
  color: inherit;
}

tasklistview taskrow {
  outline: none;
}

tasklistview taskrow entry, tasklistview taskrow entry:focus, tasklistview taskrow entry:disabled {
  box-shadow: none;
}

tasklistview taskrow image.dim-label {
  min-width: 16px;
}

tasklistview > box > revealer > box > button {
  margin: -5px;
}

tasklistview > box > revealer > box > button .dim-label {
  color: inherit;
}

/*******
 * Eog *
 *******/
#eog-thumb-nav scrolledwindow {
  border-top: none;
}

/*************
 * Evolution *
 *************/
frame.taskbar > border {
  border-style: solid none none;
}

box.vertical > paned.horizontal notebook widget .frame {
  border-style: none;
}

/***********
 * Fractal *
 ***********/
.background.csd.main-window .sidebar.rooms-sidebar {
  border-bottom-left-radius: 12px;
}

/********
 * Gitg *
 ********/
frame.commit-frame > border {
  border-style: solid none none;
}

/**************
 * Characters *
 **************/
box.dialog-vbox scrolledwindow.related {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

list.categories {
  background-image: image(#FAFAFA);
}

/*********
 * Boxes *
 *********/
.transparent-bg + stack overlay > label {
  min-height: 24px;
  padding: 0 4px;
  border-radius: 6px;
  background-color: #242424;
  color: #FFFFFF;
}

/**************
 * Calculator *
 **************/
button.title label {
  min-height: 34px;
}

/*********
 * Geary *
 *********/
window.background.csd.geary-main-window > deck > overlay > box.vertical > paned.horizontal > box.sidebar.vertical,
window#GearyMainWindow.background.csd > deck > overlay > box.vertical > paned.horizontal > box.sidebar.vertical {
  border-bottom-left-radius: 12px;
}

window.background.csd.geary-main-window > deck > overlay > box.vertical > paned.horizontal > box.sidebar.vertical statusbar,
window#GearyMainWindow.background.csd > deck > overlay > box.vertical > paned.horizontal > box.sidebar.vertical statusbar {
  border-bottom-left-radius: 12px;
}

window.background.csd.geary-main-window stack#conversation_viewer,
window#GearyMainWindow.background.csd stack#conversation_viewer {
  border-bottom-right-radius: 12px;
}

window.background.csd.geary-main-window stack#conversation_viewer scrolledwindow.geary-conversation-scroller viewport.frame list.conversation-listbox,
window#GearyMainWindow.background.csd stack#conversation_viewer scrolledwindow.geary-conversation-scroller viewport.frame list.conversation-listbox {
  background: none;
  border-bottom-right-radius: 12px;
}

window.background.csd.geary-main-window stack#conversation_viewer .geary-expanded,
window#GearyMainWindow.background.csd stack#conversation_viewer .geary-expanded {
  animation: none;
  background-image: none;
}

window.background.csd.geary-main-window stack#conversation_viewer .geary-expanded > .geary-composer-embed actionbar > revealer > box,
window#GearyMainWindow.background.csd stack#conversation_viewer .geary-expanded > .geary-composer-embed actionbar > revealer > box {
  border-radius: 0;
}

window.background.csd.geary-main-window stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar,
window#GearyMainWindow.background.csd stack#conversation_viewer .geary-expanded > .geary-composer-embed headerbar {
  color: rgba(0, 0, 0, 0.87);
  background-color: #FFFFFF;
  box-shadow: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

window.background.csd.geary-main-window stack#conversation_viewer .geary-composer-box actionbar > revealer > box,
window#GearyMainWindow.background.csd stack#conversation_viewer .geary-composer-box actionbar > revealer > box {
  border-bottom-left-radius: 0;
}

.geary-main-window leaflet > separator {
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, #242424 0, #242424 46px, transparent 46px, transparent 100%);
}

.geary-accounts-editor-pane frame:not(.geary-signature) > border,
.geary-accounts-editor-pane scrolledwindow.frame {
  border: none;
}

.geary-main-window.unified > deck > overlay > .geary-main-layout {
  background-color: #FFFFFF;
}

.geary-main-window.unified > deck > overlay > .geary-main-layout > leaflet > headerbar,
.geary-main-window.unified > deck > overlay > .geary-main-layout > leaflet > leaflet > headerbar {
  box-shadow: inset 0 -1px rgba(0, 0, 0, 0.12);
}

.geary-main-window.unified > deck > overlay > .geary-main-layout > leaflet > separator.sidebar,
.geary-main-window.unified > deck > overlay > .geary-main-layout > leaflet > leaflet > separator.sidebar {
  background-color: #242424;
  box-shadow: inset 0 -1px rgba(0, 0, 0, 0.12);
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1), color 75ms cubic-bezier(0, 0, 0.2, 1), box-shadow 75ms cubic-bezier(0, 0, 0.2, 1);
}

.geary-main-window.unified > deck > overlay > .geary-main-layout > leaflet > separator.sidebar:backdrop,
.geary-main-window.unified > deck > overlay > .geary-main-layout > leaflet > leaflet > separator.sidebar:backdrop {
  background-color: #2C2C2C;
}

.geary-main-window.unified > deck > overlay > .geary-main-layout > leaflet > leaflet > box.vertical + separator.sidebar {
  min-width: 1px;
  background-color: rgba(0, 0, 0, 0.12);
}

.geary-main-window.unified frame.geary-conversation-frame scrolledwindow {
  padding: 3px;
}

.geary-main-window.unified frame.geary-conversation-frame scrolledwindow treeview.view {
  border: 1px solid transparent;
  border-radius: 6px;
  padding: 6px;
}

.geary-main-window.unified frame.geary-conversation-frame scrolledwindow treeview.view:selected, .geary-main-window.unified frame.geary-conversation-frame scrolledwindow treeview.view:active {
  border-radius: 6px;
  background-color: @accent_color;
  color: #FFFFFF;
}

.geary-main-window.unified separator.geary-sidebar-pane-separator {
  min-width: 1px;
  background-color: rgba(0, 0, 0, 0.12);
}

.geary-main-window.unified geary-conversation-viewer#conversation_viewer list.background.conversation-listbox.content > row.activatable {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-bottom-width: 0;
  background-color: #FFFFFF;
}

.geary-main-window.unified geary-conversation-viewer#conversation_viewer list.background.conversation-listbox.content > row.activatable:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.geary-main-window.unified geary-conversation-viewer#conversation_viewer list.background.conversation-listbox.content .geary-attachment-pane {
  border-radius: 0 0 8px 8px;
}

.geary-main-window.unified geary-conversation-viewer#conversation_viewer list.background.conversation-listbox.content .geary-attachment-pane actionbar.background {
  background-color: transparent;
}

.geary-main-window.unified geary-conversation-viewer#conversation_viewer list.background.conversation-listbox.content .geary-attachment-pane actionbar.background > revealer > box {
  border-radius: 0 0 6px 6px;
}

/**************
 * Extensions *
 **************/
window.background.csd stack stack stack frame > border,
window.background.csd > stack > stack > box > frame > border,
window.background.csd > stack > stack > box > box > frame > border,
window.background.csd > stack > box > stack > box > frame > border,
window.background.csd > stack > box > stack > scrolledwindow > viewport frame > border,
window.background.csd > stack > box > stack > box > scrolledwindow > viewport > frame > border,
window.background.csd > stack > grid > scrolledwindow > viewport > box > frame > border {
  border: none;
}

window.background.csd > stack > box > box > list,
window.background.csd > stack > box > stack > scrolledwindow > viewport > list {
  border-bottom-left-radius: 12px;
}

window.background.csd > stack > box > .sidebar > scrolledwindow > viewport > list {
  padding: 0 0;
}

/***********
 * Dialogs *
 ***********/
dialog.background.csd > box.vertical.dialog-vbox > grid.horizontal > scrolledwindow.frame > viewport.frame list:first-child {
  border-radius: 0 0 0 12px;
}

dialog.background.csd > box.vertical.dialog-vbox > grid.horizontal > scrolledwindow.frame > viewport.frame list:last-child {
  border-radius: 0 0 12px 0;
}

dialog.background.csd > box.vertical.dialog-vbox > stack > scrolledwindow,
dialog.background.csd > box.vertical.dialog-vbox > stack > stack > scrolledwindow {
  border-radius: 0 0 12px 12px;
  background-color: #FFFFFF;
}

dialog.background.csd > box.vertical.dialog-vbox > stack > scrolledwindow iconview.view:not(:hover):not(:selected):not(:active),
dialog.background.csd > box.vertical.dialog-vbox > stack > stack > scrolledwindow iconview.view:not(:hover):not(:selected):not(:active) {
  background-color: transparent;
}

dialog.background.csd > box.vertical.dialog-vbox > stack > scrolledwindow > viewport.frame > list {
  border-radius: 0 0 12px 12px;
}

dialog.background.csd > box.vertical.dialog-vbox > stack > scrolledwindow > viewport.frame > list row.activatable:not(:hover):not(:selected):not(:active) {
  background-color: transparent;
}

dialog.background.csd > box.vertical.dialog-vbox > stack toolbar.toolbar {
  border-radius: 0 0 12px 12px;
}

dialog.background.csd > box.vertical.dialog-vbox > notebook > stack {
  border-radius: 0 0 12px 12px;
}

dialog.background.csd stack scrolledwindow.frame {
  border-radius: 6px;
}

dialog.background.csd stack scrolledwindow.frame textview.view {
  border-radius: 6px;
}

dialog.background.csd stack scrolledwindow.frame textview.view > text {
  background: none;
}

dialog.background.csd stack scrolledwindow viewport.frame.view {
  border-radius: 6px;
}

window.background.csd.unified {
  background-color: #FFFFFF;
}

window.background.csd.unified headerbar {
  box-shadow: inset 0 -1px rgba(0, 0, 0, 0.12);
}

window.background.csd.unified > decoration-overlay {
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

window.background.csd.unified,
window.background.csd.unified > decoration,
window.background.csd.unified > decoration-overlay {
  border-radius: 12px;
}

window.background.csd.unified.tiled > decoration-overlay, window.background.csd.unified.tiled-top > decoration-overlay, window.background.csd.unified.tiled-right > decoration-overlay, window.background.csd.unified.tiled-bottom > decoration-overlay, window.background.csd.unified.tiled-left > decoration-overlay, window.background.csd.unified.maximized > decoration-overlay, window.background.csd.unified.fullscreen > decoration-overlay {
  box-shadow: none;
}

window.background.csd.unified.tiled,
window.background.csd.unified.tiled > decoration,
window.background.csd.unified.tiled > decoration-overlay, window.background.csd.unified.tiled-top,
window.background.csd.unified.tiled-top > decoration,
window.background.csd.unified.tiled-top > decoration-overlay, window.background.csd.unified.tiled-right,
window.background.csd.unified.tiled-right > decoration,
window.background.csd.unified.tiled-right > decoration-overlay, window.background.csd.unified.tiled-bottom,
window.background.csd.unified.tiled-bottom > decoration,
window.background.csd.unified.tiled-bottom > decoration-overlay, window.background.csd.unified.tiled-left,
window.background.csd.unified.tiled-left > decoration,
window.background.csd.unified.tiled-left > decoration-overlay, window.background.csd.unified.maximized,
window.background.csd.unified.maximized > decoration,
window.background.csd.unified.maximized > decoration-overlay, window.background.csd.unified.fullscreen,
window.background.csd.unified.fullscreen > decoration,
window.background.csd.unified.fullscreen > decoration-overlay {
  border-radius: 0;
}

overlay > revealer.left > scrolledwindow.frame, overlay > revealer.right > scrolledwindow.frame {
  border-style: none;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 15px 16px 2px rgba(0, 0, 0, 0.14), 0 6px 18px 5px rgba(0, 0, 0, 0.12);
}

overlay > revealer.left > scrolledwindow.frame {
  margin-right: 32px;
}

overlay > revealer.right > scrolledwindow.frame {
  margin-left: 32px;
}

.terminix-session-sidebar,
.tilix-session-sidebar {
  background-image: image(#FFFFFF);
}

.terminal-titlebar button {
  border-radius: 0;
}

button.image-button.session-new-button {
  min-width: 30px;
}

notebook.tilix-background tab > box > stack {
  margin: -6px;
}

button.flat.tilix-small-button {
  min-height: 20px;
  min-width: 16px;
}

.terminator-terminal-window paned > separator {
  background-color: #FFFFFF;
}

.terminator-terminal-window notebook.frame {
  border-style: none;
}

#live_installer .menubar progressbar trough {
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.12);
}

.meld-notebook, .meld-notebook > stack {
  background: none;
  border-radius: 0 0 12px 12px;
}

.meld-notebook-child {
  background-color: #FAFAFA;
  border-radius: 0 0 12px 12px;
}

statusbar.meld-status-bar {
  background: none;
}

window.background > box.vertical > scrolledwindow > widget toolbar {
  padding: 2px;
}

window.background > box.vertical > scrolledwindow > widget toolbar separator,
window.background > box.vertical > scrolledwindow > widget toolbar button {
  margin: 2px;
}

window.background > box.vertical > scrolledwindow > widget toolbar button {
  border-radius: 6px;
}

window.background.chromium {
  background-color: #FFFFFF;
}

window.background.chromium entry,
window.background.chromium > button {
  border: 1px solid #e0e0e0;
}

window.background.chromium > button {
  color: @accent_color;
}

window.background.chromium > button:disabled {
  color: rgba(0, 0, 0, 0.3);
}

window.background.chromium menubar,
window.background.chromium headerbar {
  color: rgba(255, 255, 255, 0.7);
}

window.background.chromium headerbar.titlebar {
  padding: 0 12px;
}

window.background.chromium headerbar.titlebar button:active {
  background-color: alpha(currentColor, 0.12);
}

window.background.chromium spinner {
  color: @accent_color;
}

window.background.chromium textview.view {
  background-color: transparent;
}

window.background.chromium treeview.view.cell:selected:focus {
  background-color: @accent_color;
  color: #FFFFFF;
}

window.background.chromium treeview.view button {
  border: 1px solid rgba(0, 0, 0, 0.26);
  background-color: #FFFFFF;
}

window.background.chromium menu {
  border-color: #cccccc;
}

window.background.chromium menu menuitem {
  border-radius: 0;
}

tooltip.background.chromium {
  background-color: #343434;
}

#MozillaGtkWidget decoration {
  border: none;
}

#MozillaGtkWidget > widget text {
  background-color: #FFFFFF;
}

#MozillaGtkWidget > widget text:selected {
  background-color: @accent_color;
  color: #FFFFFF;
}

#MozillaGtkWidget > widget > separator {
  color: #e0e0e0;
}

#MozillaGtkWidget > widget > scrollbar {
  background-clip: border-box;
}

#MozillaGtkWidget > widget > frame > border {
  border-color: #e0e0e0;
}

#MozillaGtkWidget > widget > entry,
#MozillaGtkWidget > widget > button > button {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: none;
}

#MozillaGtkWidget > widget > entry:disabled,
#MozillaGtkWidget > widget > button > button:disabled {
  border-color: rgba(0, 0, 0, 0.12);
}

#MozillaGtkWidget > widget > entry {
  min-height: 30px;
  background-color: #FFFFFF;
}

#MozillaGtkWidget > widget > entry:focus {
  border-color: @accent_color;
  box-shadow: inset 0 0 0 1px @accent_color;
}

#MozillaGtkWidget > widget > entry:disabled {
  background-color: #FAFAFA;
}

#MozillaGtkWidget > widget > button > button {
  padding: 4px 8px;
  background-size: auto;
}

#MozillaGtkWidget > widget > button > button:hover {
  box-shadow: inset 0 0 0 9999px alpha(currentColor, 0.08);
}

#MozillaGtkWidget > widget > button > button:active {
  background-image: image(alpha(currentColor, 0.12));
}

#MozillaGtkWidget > widget > checkbutton > check,
#MozillaGtkWidget > widget > radiobutton > radio {
  margin: 0;
  padding: 0;
}

#MozillaGtkWidget > widget > checkbutton > check:not(:checked):not(:indeterminate),
#MozillaGtkWidget > widget > radiobutton > radio:not(:checked):not(:indeterminate) {
  color: #464646;
}

#MozillaGtkWidget > widget > checkbutton > check:not(:checked):not(:indeterminate):hover, #MozillaGtkWidget > widget > checkbutton > check:not(:checked):not(:indeterminate):active,
#MozillaGtkWidget > widget > radiobutton > radio:not(:checked):not(:indeterminate):hover,
#MozillaGtkWidget > widget > radiobutton > radio:not(:checked):not(:indeterminate):active {
  color: #727272;
}

#MozillaGtkWidget > widget > checkbutton > check:not(:checked):not(:indeterminate):disabled,
#MozillaGtkWidget > widget > radiobutton > radio:not(:checked):not(:indeterminate):disabled {
  color: rgba(70, 70, 70, 0.5);
}

#MozillaGtkWidget menu {
  border: none;
}

#MozillaGtkWidget > widget > menubar {
  color: rgba(255, 255, 255, 0.7);
}

#MozillaGtkWidget > widget > menubar:hover {
  color: #FFFFFF;
}

#MozillaGtkWidget > widget > menubar:disabled {
  color: rgba(255, 255, 255, 0.32);
}

#MozillaGtkWidget > widget > frame {
  color: #e0e0e0;
}

#MozillaGtkWidget menu > separator {
  color: #e0e0e0;
}

window.background:not(.csd) > window > menu menuitem {
  transition: none;
}

#ToolboxCommon > #AuxToolbox #StyleSwatch {
  font-size: smaller;
}

#ToolboxCommon > #AuxToolbox #Kludge {
  padding: 0;
}

#ToolboxCommon > #AuxToolbox spinbutton,
#ToolboxCommon > #AuxToolbox entry {
  min-height: 32px;
}

#ToolboxCommon > #AuxToolbox button:not(.up):not(.down) {
  min-height: 24px;
  min-width: 16px;
  padding: 4px 8px;
}

#ToolboxCommon > #AuxToolbox spinbutton button {
  border-width: 4px;
}

#ToolboxCommon > toolbar.vertical {
  margin-top: -4px;
}

#ToolboxCommon > toolbar.vertical button {
  min-height: 24px;
  min-width: 24px;
  padding: 4px;
}

#TopToolbars .toolbar {
  background: none;
}

#Statusbar {
  padding: 3px 0 3px 8px;
}

#Statusbar spinbutton, #Statusbar box > button.flat {
  margin: 3px 0;
}

#CanvasTable button {
  min-height: 16px;
  min-width: 16px;
  padding: 0;
}

#CanvasTable #HorizontalScrollbar {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

#CanvasTable #VerticalScrollbar:dir(ltr) {
  border-left: 1px solid rgba(0, 0, 0, 0.12);
}

#CanvasTable #VerticalScrollbar:dir(rtl) {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

#Canvas_and_Dock frame > border {
  border: none;
}

#Canvas_and_Dock widget > widget > button.flat {
  min-height: 16px;
  min-width: 16px;
  padding: 4px;
}

#Canvas_and_Dock widget > widget > box.horizontal image {
  padding: 4px;
}

#Canvas_and_Dock box.horizontal > box.vertical > button.flat {
  min-height: 16px;
  min-width: 24px;
  padding: 8px 4px;
}

window.bright.symbolic scrolledwindow > viewport.frame > notebook tabs > tab {
  padding: 3px 6px;
}

window.bright.symbolic scrolledwindow > viewport.frame > notebook tabs > tab button.close-button {
  margin: 4px 0;
}

popover#ContextMenu {
  border-radius: 12px;
  background-clip: content-box;
}

popover#ContextMenu modelbutton.flat {
  border-radius: 6px;
}

window.background > grid > widget > widget > scrolledwindow > viewport > grid > box > box > frame > box {
  background-color: #FFFFFF;
}

window.background.csd > box.vertical > overlay > stack > box.vertical > box.horizontal > revealer > stack > list,
window.background.csd > box.vertical > overlay > stack > box.vertical > box.horizontal > revealer > stack > scrolledwindow > viewport.frame > list,
window.background.csd > box.vertical > overlay > stack > box.vertical > box.horizontal > revealer > stack > box.vertical > stack > scrolledwindow > viewport.frame > list {
  border: none;
  border-radius: 0;
}

window.background.csd > box.vertical > overlay > stack > box.vertical > box.horizontal > revealer > stack > list > row.activatable,
window.background.csd > box.vertical > overlay > stack > box.vertical > box.horizontal > revealer > stack > scrolledwindow > viewport.frame > list > row.activatable,
window.background.csd > box.vertical > overlay > stack > box.vertical > box.horizontal > revealer > stack > box.vertical > stack > scrolledwindow > viewport.frame > list > row.activatable {
  border-radius: 6px;
}

window.background:not(.csd):not(.solid-csd) > button:not(:hover):not(:active):not(:checked):not(:disabled):not(.flat) {
  background-color: #FAFAFA;
}

window.background:not(.csd) > box > widget > widget > widget > widget > widget > widget > widget > scrolledwindow entry:focus {
  background-color: #f2f2f2;
}

/*********
 * Unity *
 *********/
UnityDecoration {
  -UnityDecoration-extents: 28px 0 0 0;
  -UnityDecoration-input-extents: 8px;
  -UnityDecoration-shadow-offset-x: 0;
  -UnityDecoration-shadow-offset-y: 3px;
  -UnityDecoration-active-shadow-color: rgba(0, 0, 0, 0.48);
  -UnityDecoration-active-shadow-radius: 18px;
  -UnityDecoration-inactive-shadow-color: rgba(0, 0, 0, 0.32);
  -UnityDecoration-inactive-shadow-radius: 6px;
  -UnityDecoration-glow-size: 8px;
  -UnityDecoration-glow-color: @accent_color;
  -UnityDecoration-title-indent: 4px;
  -UnityDecoration-title-fade: 32px;
  -UnityDecoration-title-alignment: 0.0;
}

UnityDecoration .top {
  padding: 0 2px;
  border-style: none;
  border-radius: 12px 12px 0 0;
  box-shadow: inset 0 1px rgba(255, 255, 255, 0.1);
  background-color: #242424;
  color: #FFFFFF;
}

UnityDecoration .top:backdrop {
  background-color: #2C2C2C;
  color: rgba(255, 255, 255, 0.7);
}

UnityDecoration .menuitem {
  color: rgba(255, 255, 255, 0.7);
}

UnityDecoration .menuitem:hover {
  box-shadow: inset 0 -2px @accent_color;
  background-color: transparent;
  color: #FFFFFF;
}

.background:not(.csd) headerbar:not(.titlebar) {
  border-radius: 0;
  box-shadow: 0 2px 3px -2px rgba(0, 0, 0, 0.3), 0 1px 2px -1px rgba(0, 0, 0, 0.24), 0 1px 2px -1px rgba(0, 0, 0, 0.17);
}

.background:not(.csd) headerbar:not(.titlebar).inline-toolbar {
  border-style: none;
}

UnityPanelWidget,
.unity-panel {
  background-color: #212121;
  color: #FFFFFF;
}

UnityPanelWidget:backdrop,
.unity-panel:backdrop {
  color: rgba(255, 255, 255, 0.7);
}

.unity-panel.menuitem,
.unity-panel .menuitem {
  color: rgba(255, 255, 255, 0.7);
}

.unity-panel.menubar.menuitem:hover,
.unity-panel.menubar .menuitem *:hover {
  box-shadow: inset 0 -2px @accent_color;
  background-color: transparent;
  color: #FFFFFF;
}

.menu IdoPlaybackMenuItem.menuitem:active {
  -gtk-icon-source: -gtk-icontheme("process-working-symbolic");
  animation: spin 1s linear infinite;
  color: @accent_color;
}

.lightdm.menu {
  background-image: none;
  background-color: rgba(0, 0, 0, 0.45);
  border: none;
  border-radius: 12px;
  padding: 0;
  color: white;
}

.lightdm.menu .menuitem *,
.lightdm.menu .menuitem.check:active,
.lightdm.menu .menuitem.radio:active {
  color: white;
}

.lightdm.menubar {
  color: rgba(255, 255, 255, 0.8);
  background-image: none;
  background-color: rgba(0, 0, 0, 0.5);
}

.lightdm.menubar > .menuitem {
  padding: 2px 6px;
}

.lightdm-combo .menu {
  background-color: #FFFFFF;
  border-radius: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.87);
}

/**************
 * Mate-Panel *
 **************/
.mate-panel-menu-bar menubar,
#PanelApplet-window-menu-applet-button {
  background-color: transparent;
}

.mate-panel-menu-bar {
  background-color: #212121;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.mate-panel-menu-bar button {
  min-height: 16px;
  min-width: 16px;
  padding: 0;
  border-radius: 0;
}

PanelToplevel.horizontal > grid > button {
  min-width: 24px;
}

PanelToplevel.vertical > grid > button {
  min-height: 24px;
}

PanelSeparator {
  color: rgba(255, 255, 255, 0.12);
}

MatePanelAppletFrameDBus {
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.12);
}

.mate-panel-menu-bar.horizontal MatePanelAppletFrameDBus {
  border-width: 0 1px;
}

.mate-panel-menu-bar.vertical MatePanelAppletFrameDBus {
  border-width: 1px 0;
}

.mate-panel-menu-bar menubar > menuitem {
  color: rgba(255, 255, 255, 0.7);
}

.mate-panel-menu-bar menubar > menuitem:hover {
  color: #FFFFFF;
}

.mate-panel-menu-bar menubar > menuitem:disabled {
  color: rgba(255, 255, 255, 0.32);
}

.mate-panel-menu-bar.horizontal menubar > menuitem {
  padding: 0 8px;
}

.mate-panel-menu-bar.vertical menubar > menuitem {
  padding: 8px 0;
}

.mate-panel-menu-bar menubar menu > menuitem {
  min-height: 28px;
  padding: 0 6px;
}

.mate-panel-menu-bar #PanelApplet button {
  -GtkWidget-window-dragging: true;
}

.mate-panel-menu-bar #tasklist-button {
  border-image: radial-gradient(circle closest-corner at center calc(100% - 1px), currentColor 0%, transparent 0%) 0 0 0/0 0 0px;
}

.mate-panel-menu-bar #tasklist-button:checked {
  border-image: radial-gradient(circle closest-corner at center calc(100% - 1px), currentColor 100%, transparent 0%) 0 0 2/0 0 2px;
}

.mate-panel-menu-bar #tasklist-button image:dir(ltr), .mate-panel-menu-bar #tasklist-button label:dir(rtl) {
  padding-left: 4px;
}

.mate-panel-menu-bar #tasklist-button label:dir(ltr), .mate-panel-menu-bar #tasklist-button image:dir(rtl) {
  padding-right: 4px;
}

.mate-panel-menu-bar.vertical #tasklist-button {
  min-height: 32px;
}

.mate-panel-menu-bar.horizontal #showdesktop-button image {
  min-width: 24px;
  padding: 0 4px;
}

.mate-panel-menu-bar.vertical #showdesktop-button image {
  min-height: 24px;
  padding: 4px 0;
}

PanelApplet.wnck-applet .wnck-pager {
  background-color: transparent;
  color: @accent_color;
}

PanelApplet.wnck-applet .wnck-pager:hover {
  background-color: alpha(currentColor, 0.08);
}

PanelApplet.wnck-applet .wnck-pager:active {
  background-color: alpha(currentColor, 0.12);
}

PanelApplet.wnck-applet .wnck-pager:selected {
  background-color: @accent_color;
}

.mate-panel-menu-bar.horizontal #clock-applet-button label {
  padding: 0 8px;
}

.mate-panel-menu-bar.vertical #clock-applet-button label {
  padding: 8px 0;
}

#MatePanelPopupWindow {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 7px;
  box-shadow: inset 0 1px rgba(255, 255, 255, 0.15);
  background-color: #FFFFFF;
}

#MatePanelPopupWindow frame > border {
  border-style: none;
}

#MatePanelPopupWindow calendar {
  border-style: none;
}

#MatePanelPopupWindow calendar:not(:selected) {
  background-color: transparent;
}

#MatePanelPopupWindow calendar + box {
  margin-top: -5px;
  padding-top: 5px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

#MatePanelPopupWindow expander > title {
  min-height: 32px;
}

#MatePanelPopupWindow button {
  padding: 4px 16px;
}

#MatePanelPopupWindow > frame > box > box > box > widget {
  color: rgba(0, 0, 0, 0.12);
}

na-tray-applet {
  -NaTrayApplet-icon-padding: 3px;
  -NaTrayApplet-icon-size: 16;
}

.mate-panel-menu-bar {
  -PanelMenuBar-icon-visible: true;
}

.mate-panel-applet-slider {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 7px;
  box-shadow: inset 0 1px rgba(255, 255, 255, 0.15);
  background-color: #FFFFFF;
}

.mate-panel-applet-slider frame > border {
  border-style: none;
}

#PanelApplet:not(:selected) > box {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
}

#PanelApplet:selected > box {
  background-color: alpha(currentColor, 0.1);
  color: #FFFFFF;
}

#mate-menu {
  border: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
}

#mate-menu button {
  min-height: 24px;
  min-width: 24px;
  padding: 4px 0;
  color: rgba(0, 0, 0, 0.87);
  font-weight: normal;
}

#mate-menu button:not(.flat) {
  background-color: alpha(currentColor, 0.1);
}

#mate-menu button image,
#mate-menu button label + label {
  color: rgba(0, 0, 0, 0.6);
}

#mate-menu entry {
  margin: 0 0 4px;
}

#mate-menu entry image {
  margin: 0;
}

#mate-menu entry + button {
  margin: 0 4px 4px;
  padding: 5px;
}

.brisk-menu {
  box-shadow: inset 0 1px rgba(255, 255, 255, 0.15);
  background-color: #FFFFFF;
}

.brisk-menu entry {
  margin-bottom: -2px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  border-image: none;
  box-shadow: none;
  background-color: transparent;
}

.brisk-menu entry + box > box:dir(ltr) {
  margin-right: -2px;
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}

.brisk-menu entry + box > box:dir(rtl) {
  margin-left: -2px;
  border-left: 1px solid rgba(0, 0, 0, 0.12);
}

.brisk-menu .categories-list {
  padding-top: 4px;
}

.brisk-menu .categories-list button {
  margin: 0 4px;
}

.brisk-menu .categories-list button:checked {
  color: @accent_color;
}

.brisk-menu .session-button {
  padding: 11px;
}

.brisk-menu .frame {
  border-style: none;
}

.brisk-menu .apps-list {
  padding: 4px 0;
  background-color: transparent;
}

.brisk-menu .apps-list row {
  padding: 0;
}

.brisk-menu .apps-list row:hover {
  box-shadow: none;
}

.brisk-menu .apps-list button {
  border-radius: 0;
  color: rgba(0, 0, 0, 0.87);
  font-weight: normal;
}

/*********************
 * CAJA File manager *
 *********************/
.caja-navigation-window button.toggle.image-button {
  border-radius: 6px;
}

.caja-pathbar button {
  margin: 0 -1px 0 -2px;
}

.caja-pathbar button.slider-button {
  min-width: 24px;
}

.caja-pathbar button > widget {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
  -GtkArrow-arrow-scaling: 1;
}

.caja-side-pane notebook viewport.frame,
.caja-side-pane notebook widget .vertical {
  background-color: #FFFFFF;
}

.caja-side-pane notebook,
.caja-notebook {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.caja-side-pane notebook .frame,
.caja-notebook .frame {
  border-style: none;
}

.caja-canvas-item {
  border-radius: 6px;
}

.caja-desktop.view .entry,
.caja-navigation-window .view .entry {
  border: none;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.04);
  background-image: none;
  color: rgba(0, 0, 0, 0.87);
}

.caja-desktop.view .entry:selected,
.caja-navigation-window .view .entry:selected {
  background-color: alpha(currentColor, 0.06);
}

.caja-desktop.view .entry {
  background-color: #2C2C2C;
  color: #FFFFFF;
  caret-color: currentColor;
}

.caja-desktop.view .entry:selected {
  background-color: alpha(currentColor, 0.06);
}

.caja-navigation-window statusbar {
  margin: 0 -10px;
  padding: 0 4px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.caja-notebook frame > border {
  border-style: none;
}

#caja-extra-view-widget {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
}

#caja-extra-view-widget > box > box > label {
  font-weight: bold;
}

/*********
 * Pluma *
 *********/
.pluma-window statusbar {
  margin: 0 -10px;
  padding: 0 4px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.pluma-window statusbar frame > border {
  border-style: none;
}

.pluma-window statusbar frame button.flat {
  padding: 0 4px;
  border-radius: 0;
}

.pluma-window statusbar frame button.flat widget {
  -gtk-icon-source: -gtk-icontheme("pan-down-symbolic");
  -GtkArrow-arrow-scaling: 1;
}

.pluma-print-preview toolbar {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.pluma-window paned.horizontal box.vertical box.horizontal button.flat {
  margin: 1px;
}

.pluma-window paned.horizontal box.vertical .frame {
  border-style: none;
}

.pluma-window paned.horizontal box.vertical notebook.frame {
  margin-top: -1px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.pluma-window paned.horizontal box.vertical notebook.frame box.vertical toolbar.horizontal {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

/*********
 * Atril *
 *********/
.atril-window paned.horizontal box.vertical .frame {
  border-style: none;
}

.atril-window paned.horizontal box.vertical notebook .frame {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

/* mate-screensaver lock dialog */
.lock-dialog {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 7px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15), 0 3px 3px 0 rgba(0, 0, 0, 0.18), 0 3px 6px 0 rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.15);
  background-color: #FFFFFF;
}

.lock-dialog frame > border {
  border-style: none;
}

.lock-dialog button:not(:disabled) {
  color: @accent_color;
}

/* multimedia OSD */
MsdOsdWindow.background.osd {
  border-radius: 6px;
  background-color: rgba(52, 52, 52, 0.9);
  color: #FFFFFF;
}

MsdOsdWindow.background.osd .trough {
  border-radius: 0;
  background-color: rgba(255, 255, 255, 0.12);
}

MsdOsdWindow.background.osd .progressbar {
  border-radius: 0;
  background-color: @accent_color;
}

/******************
 * Budgie Desktop *
 ******************/
.budgie-container {
  background-color: transparent;
}

.budgie-settings-window list.sidebar {
  background: none;
  border-radius: 0 0 0 12px;
}

.budgie-settings-window buttonbox.inline-toolbar {
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.budgie-settings-window buttonbox.inline-toolbar button {
  border-radius: 6px;
}

dialog.background > .dialog-vbox > scrolledwindow > viewport.frame > list {
  border-right: 1px solid #e0e0e0;
}

.budgie-popover {
  border-color: #FFFFFF;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15), 0 3px 3px 0 rgba(0, 0, 0, 0.18), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.12);
  background-clip: border-box;
  background-color: #FFFFFF;
  border-radius: 12px;
}

.budgie-popover frame.container {
  padding: 6px;
  border: none;
}

.budgie-popover frame.container .container {
  padding: 0;
}

.budgie-popover list, .budgie-popover row {
  padding: 0;
}

.budgie-popover separator.horizontal {
  margin: 3px 0;
}

.budgie-popover border {
  border: none;
}

.budgie-popover list {
  background-color: transparent;
}

.budgie-popover row:hover {
  box-shadow: none;
}

.budgie-popover scrolledwindow.sidebar:not(.categories) {
  background: none;
  border-right: none;
}

.budgie-popover scrolledwindow.sidebar:not(.categories) list {
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 3px;
  padding: 3px 0;
}

.budgie-popover scrolledwindow.sidebar:not(.categories) list > row.activatable {
  padding: 6px 8px;
}

.budgie-popover scrolledwindow.sidebar + separator {
  margin: 0;
  background-color: transparent;
  min-width: 0;
  min-height: 0;
}

.budgie-popover scrolledwindow.sidebar + separator + scrolledwindow {
  margin-left: 3px;
}

.budgie-popover scrolledwindow.sidebar + separator + scrolledwindow list > row.activatable {
  border-radius: 3px;
}

.budgie-popover treeview.view.sidebar {
  border-right: none;
  background: none;
}

.budgie-popover treeview.view.sidebar:hover {
  background-color: alpha(currentColor, 0.08);
}

.budgie-popover treeview.view.sidebar:selected {
  background-color: alpha(currentColor, 0.12);
}

.budgie-popover.budgie-menu .container {
  padding: 0;
}

.budgie-popover.budgie-menu .sidebar,
.budgie-popover.budgie-menu scrollbar,
.budgie-popover.budgie-menu entry.search {
  background-color: transparent;
}

.budgie-popover.budgie-menu entry.search {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  border-image: none;
  border-radius: 0;
  box-shadow: none;
  font-size: 120%;
}

.budgie-popover.budgie-menu scrolledwindow.sidebar.categories {
  background-color: rgba(0, 0, 0, 0.04);
  padding-bottom: 12px;
}

.budgie-popover.budgie-menu scrolledwindow.sidebar.categories button.flat.radio.category-button {
  border-radius: 0;
}

.budgie-popover.budgie-menu scrolledwindow > viewport.frame > list > row.activatable > button.flat {
  border-radius: 0;
}

.budgie-popover.budgie-menu list.left-overlay-menu {
  border-radius: 12px;
  background-color: #FFFFFF;
  padding: 6px;
  margin: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15), 0 3px 3px 0 rgba(0, 0, 0, 0.18), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.12);
}

.budgie-popover.budgie-menu list.left-overlay-menu > row.activatable {
  border-radius: 6px;
}

.budgie-popover.budgie-menu list.left-overlay-menu > row.activatable:not(:last-child) {
  margin-bottom: 3px;
}

.budgie-popover.budgie-menu list.left-overlay-menu > row.activatable button.menuitem {
  border-radius: 6px;
}

.budgie-popover.budgie-menu .budgie-menu-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  padding: 6px;
}

.budgie-popover.budgie-menu .budgie-menu-footer button.flat {
  padding: 3px;
  border-radius: 6px;
}

.budgie-popover.budgie-menu .budgie-menu-footer button.flat.user-icon-button {
  padding-right: 9px;
}

.budgie-popover.budgie-menu .budgie-menu-footer button.flat.image-button {
  border-radius: 9999px;
  padding: 6px;
  min-height: 16px;
  min-width: 16px;
  margin-left: 9px;
  background-clip: border-box;
}

.budgie-popover.user-menu list,
.budgie-popover.user-menu row {
  border: none;
  background: none;
  box-shadow: none;
}

.budgie-popover.user-menu > frame.container > box.vertical row.activatable:first-child {
  margin-bottom: 0;
  outline-width: 0;
  border-radius: 6px;
}

.budgie-popover.user-menu > frame.container > box.vertical row.activatable:first-child button.indicator-item {
  transition: none;
  animation: none;
}

.budgie-popover.sound-popover .container {
  padding: 0;
}

.budgie-popover.sound-popover separator {
  margin: 3px 0;
}

.budgie-popover.night-light-indicator .view-header {
  margin: 0 6px;
}

.budgie-popover.places-menu .name-button image:dir(ltr) {
  margin-right: 3px;
}

.budgie-popover.places-menu .name-button image:dir(rtl) {
  margin-left: 3px;
}

.budgie-popover.places-menu .unmount-button {
  margin: 2px;
  padding: 0;
}

.budgie-popover.places-menu .places-list:not(.always-expand) {
  margin-top: 3px;
  padding-top: 3px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.budgie-popover.places-menu .alternative-label {
  padding: 3px;
  font-size: 15px;
}

.budgie-popover.workspace-popover flowboxchild {
  padding: 0;
}

.workspace-switcher .workspace-layout {
  border: 0 solid rgba(255, 255, 255, 0.12);
}

.top .workspace-switcher .workspace-layout:dir(ltr), .bottom .workspace-switcher .workspace-layout:dir(ltr) {
  border-left-width: 1px;
}

.top .workspace-switcher .workspace-layout:dir(rtl), .bottom .workspace-switcher .workspace-layout:dir(rtl) {
  border-right-width: 1px;
}

.left .workspace-switcher .workspace-layout, .right .workspace-switcher .workspace-layout {
  border-top-width: 1px;
}

.workspace-switcher .workspace-item {
  border: 0 solid rgba(255, 255, 255, 0.12);
}

.top .workspace-switcher .workspace-item:dir(ltr), .bottom .workspace-switcher .workspace-item:dir(ltr) {
  border-right-width: 1px;
}

.top .workspace-switcher .workspace-item:dir(rtl), .bottom .workspace-switcher .workspace-item:dir(rtl) {
  border-left-width: 1px;
}

.left .workspace-switcher .workspace-item, .right .workspace-switcher .workspace-item {
  border-bottom-width: 1px;
}

.workspace-switcher .workspace-item {
  transition: all 75ms cubic-bezier(0, 0, 0.2, 1);
}

.workspace-switcher .workspace-item.current-workspace {
  background-color: alpha(currentColor, 0.1);
}

.workspace-switcher .workspace-add-button {
  border-radius: 6px;
}

.budgie-panel .workspace-switcher .workspace-add-button {
  min-height: 24px;
  min-width: 24px;
  padding: 0;
}

.budgie-panel .workspace-switcher .workspace-icon-button {
  min-height: 24px;
  min-width: 24px;
  padding: 0;
  border-radius: 6px;
}

.budgie-panel {
  transition: background-color 75ms cubic-bezier(0, 0, 0.2, 1);
  background-color: #212121;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.budgie-panel.transparent {
  background-color: rgba(33, 33, 33, 0.75);
}

.budgie-panel .icon-tasklist button.launcher:checked, .budgie-panel .icon-tasklist button.launcher:active {
  color: #FFFFFF;
}

.top .budgie-panel.dock-mode {
  border-radius: 0 0 18px 18px;
}

.bottom .budgie-panel.dock-mode {
  border-radius: 18px 18px 0 0;
}

.bottom .budgie-panel.dock-mode .icon-tasklist > box > revealer:first-child > button.launcher {
  border-top-left-radius: 18px;
}

.bottom .budgie-panel.dock-mode .icon-tasklist > box > revealer:last-child > button.launcher {
  border-top-right-radius: 18px;
}

.left .budgie-panel.dock-mode {
  border-radius: 0 18px 18px 0;
}

.left .budgie-panel.dock-mode .icon-tasklist .launcher:first-child {
  border-top-right-radius: 18px;
}

.left .budgie-panel.dock-mode .icon-tasklist .launcher:last-child {
  border-bottom-right-radius: 18px;
}

.right .budgie-panel.dock-mode {
  border-radius: 18px 0 0 18px;
}

.right .budgie-panel.dock-mode .icon-tasklist .launcher:first-child {
  border-top-left-radius: 18px;
}

.right .budgie-panel.dock-mode .icon-tasklist .launcher:last-child {
  border-bottom-left-radius: 18px;
}

.budgie-panel button {
  color: rgba(255, 255, 255, 0.7);
  min-height: 24px;
  min-width: 24px;
  padding: 0;
  border-radius: 0;
}

.budgie-panel button:hover {
  color: #FFFFFF;
}

.budgie-panel button:active {
  color: rgba(255, 255, 255, 0.7);
}

.budgie-panel button.budgie-menu-launcher {
  color: rgba(255, 255, 255, 0.7);
}

.budgie-panel button.budgie-menu-launcher:focus {
  box-shadow: none;
  border: none;
  color: #FFFFFF;
}

.budgie-panel button.raven-trigger {
  color: rgba(255, 255, 255, 0.7);
}

.budgie-panel.horizontal button {
  padding: 0 6px;
}

.budgie-panel.vertical button {
  padding: 6px 0;
}

.budgie-panel separator {
  background-color: rgba(255, 255, 255, 0.12);
}

.budgie-panel .alert {
  color: #E53935;
}

.budgie-panel > box > widget > widget > image,
.budgie-panel > box > widget > widget > stack > image,
.budgie-panel > box > widget > widget > box > image {
  margin-left: 6px;
  margin-right: 6px;
}

.budgie-panel > box > widget > widget > box > image + label {
  margin-left: -4px;
}

.budgie-panel > box > widget > widget > box > widget > image {
  margin-left: 6px;
}

.budgie-panel > box > widget > widget > box > stack > widget > label {
  margin-right: 6px;
}

.budgie-panel > box > widget > widget > box > widget > widget > image {
  margin-left: 2px;
  margin-right: 2px;
}

.budgie-panel .budgie-clock-applet > widget > box,
.budgie-panel .budgie-calendar-applet > widget > box {
  padding-left: 3px;
  padding-right: 3px;
}

.budgie-panel .titlebar:not(headerbar) {
  min-height: 0;
  padding: 0;
  box-shadow: none;
  background-color: transparent;
  color: #FFFFFF;
}

.budgie-panel .titlebar:not(headerbar) button:not(.suggested-action):not(.destructive-action) {
  color: rgba(255, 255, 255, 0.7);
}

.budgie-panel .titlebar:not(headerbar) button:not(.suggested-action):not(.destructive-action):hover, .budgie-panel .titlebar:not(headerbar) button:not(.suggested-action):not(.destructive-action):active {
  color: #FFFFFF;
}

.budgie-panel menubar,
.budgie-panel .menubar {
  color: rgba(255, 255, 255, 0.7);
  box-shadow: none;
  border: none;
}

.budgie-panel menubar > menuitem,
.budgie-panel .menubar > menuitem {
  color: rgba(255, 255, 255, 0.7);
}

.budgie-panel menubar > menuitem:hover, .budgie-panel menubar > menuitem:active,
.budgie-panel .menubar > menuitem:hover,
.budgie-panel .menubar > menuitem:active {
  color: #FFFFFF;
}

.budgie-panel menubar menu separator,
.budgie-panel .menubar menu separator {
  background-color: rgba(0, 0, 0, 0.12);
}

.budgie-panel #tasklist-button {
  padding: 0 4px;
}

.budgie-panel.vertical #tasklist-button {
  min-height: 32px;
}

.budgie-panel button.flat.launcher {
  padding: 0;
}

.budgie-panel button.flat.launcher:not(:checked) {
  color: rgba(255, 255, 255, 0.5);
}

.budgie-panel button.flat.launcher:not(:checked):hover, .budgie-panel button.flat.launcher:not(:checked):active {
  color: rgba(255, 255, 255, 0.7);
}

.budgie-panel button.flat.launcher:not(:checked):disabled {
  color: rgba(255, 255, 255, 0.32);
}

.top .budgie-panel .unpinned button.flat.launcher:checked, .top .budgie-panel .pinned button.flat.launcher.running:checked {
  border-image: radial-gradient(circle closest-corner at center calc(1px), currentColor 100%, transparent 0%) 2 0 0 0/2px 0 0 0;
}

.bottom .budgie-panel .unpinned button.flat.launcher:checked, .bottom .budgie-panel .pinned button.flat.launcher.running:checked {
  border-image: radial-gradient(circle closest-corner at center calc(100% - 1px), currentColor 100%, transparent 0%) 0 0 2 0/0 0 2px 0;
}

.left .budgie-panel .unpinned button.flat.launcher:checked, .left .budgie-panel .pinned button.flat.launcher.running:checked {
  border-image: radial-gradient(circle closest-corner at calc(1px) center, currentColor 100%, transparent 0%) 0 0 0 2/0 0 0 2px;
}

.right .budgie-panel .unpinned button.flat.launcher:checked, .right .budgie-panel .pinned button.flat.launcher.running:checked {
  border-image: radial-gradient(circle closest-corner at calc(100% - 1px) center, currentColor 100%, transparent 0%) 0 2 0 0/0 2px 0 0;
}

.top .budgie-panel #tasklist-button, .budgie-panel .top #tasklist-button {
  border-image: radial-gradient(circle closest-corner at center calc(1px), currentColor 0%, transparent 0%) 0 0 0 0/0 0 0 0;
}

.top .budgie-panel #tasklist-button:checked, .budgie-panel .top #tasklist-button:checked {
  border-image: radial-gradient(circle closest-corner at center calc(1px), currentColor 100%, transparent 0%) 2 0 0 0/2px 0 0 0;
}

.bottom .budgie-panel #tasklist-button, .budgie-panel .bottom #tasklist-button {
  border-image: radial-gradient(circle closest-corner at center calc(100% - 1px), currentColor 0%, transparent 0%) 0 0 0 0/0 0 0 0;
}

.bottom .budgie-panel #tasklist-button:checked, .budgie-panel .bottom #tasklist-button:checked {
  border-image: radial-gradient(circle closest-corner at center calc(100% - 1px), currentColor 100%, transparent 0%) 0 0 2 0/0 0 2px 0;
}

.left .budgie-panel #tasklist-button, .budgie-panel .left #tasklist-button {
  border-image: radial-gradient(circle closest-corner at calc(1px) center, currentColor 0%, transparent 0%) 0 0 0 0/0 0 0 0;
}

.left .budgie-panel #tasklist-button:checked, .budgie-panel .left #tasklist-button:checked {
  border-image: radial-gradient(circle closest-corner at calc(1px) center, currentColor 100%, transparent 0%) 0 0 0 2/0 0 0 2px;
}

.right .budgie-panel #tasklist-button, .budgie-panel .right #tasklist-button {
  border-image: radial-gradient(circle closest-corner at calc(100% - 1px) center, currentColor 0%, transparent 0%) 0 0 0 0/0 0 0 0;
}

.right .budgie-panel #tasklist-button:checked, .budgie-panel .right #tasklist-button:checked {
  border-image: radial-gradient(circle closest-corner at calc(100% - 1px) center, currentColor 100%, transparent 0%) 0 2 0 0/0 2px 0 0;
}

frame.raven-frame > border {
  border-style: none;
}

.top frame.raven-frame > border {
  margin-bottom: 32px;
}

.bottom frame.raven-frame > border {
  margin-top: 32px;
}

.left frame.raven-frame > border {
  margin-right: 32px;
}

.right frame.raven-frame > border {
  margin-left: 32px;
}

.raven {
  background-color: #FFFFFF;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 15px 16px 2px rgba(0, 0, 0, 0.14), 0 6px 18px 5px rgba(0, 0, 0, 0.12);
}

.raven > box {
  margin-bottom: -10px;
}

.raven stackswitcher.linked {
  margin: 6px 16px;
}

.raven stackswitcher.linked > button:focus {
  box-shadow: none;
}

.raven .raven-header {
  min-height: 34px;
  padding: 3px;
}

.raven .raven-header.top {
  padding: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.raven .raven-header.top stackswitcher button {
  margin: -4px 0 -5px;
  padding: 0 16px;
  min-height: 24px;
}

.raven .raven-header.bottom {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.raven stack .raven-header {
  margin-top: -6px;
}

.raven stack scrolledwindow .raven-header {
  margin-top: -8px;
}

.raven .raven-background {
  border-style: solid none;
  border-width: 1px;
  border-color: rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
}

.raven .raven-background > overlay > widget > image {
  color: rgba(0, 0, 0, 0.12);
}

.raven scrolledwindow.raven-background {
  border-bottom-style: none;
}

.raven .powerstrip button {
  margin: 2px 0 1px;
  padding: 11px;
  border-radius: 9999px;
}

.raven .option-subtitle {
  font-size: smaller;
}

.raven .audio-widget scale.marks-after {
  padding-top: 0;
  padding-bottom: 0;
}

.raven .audio-widget scale.marks-after label {
  font-size: 90%;
  padding: 0;
  margin: -10px 0 0 6px;
}

.raven .audio-widget button.flat.expander-button {
  margin-top: 4px;
  margin-bottom: 4px;
}

.raven .audio-widget list.devices-list.sound-devices > row.activatable:selected, .raven .audio-widget list.devices-list.sound-devices > row.activatable:checked {
  background-color: rgba(0, 0, 0, 0.06);
  color: rgba(0, 0, 0, 0.87);
}

.raven .audio-widget list.devices-list.sound-devices > row.activatable:selected label, .raven .audio-widget list.devices-list.sound-devices > row.activatable:checked label {
  color: rgba(0, 0, 0, 0.87);
}

.raven .audio-widget list.devices-list.sound-devices > row.activatable label {
  padding-left: 12px;
}

.raven levelbar, .raven levelbar trough, .raven levelbar block {
  border-radius: 9999px;
}

calendar.raven-calendar {
  border-style: none;
  background-color: transparent;
}

calendar.raven-calendar:selected {
  border-radius: 6px;
}

.raven-mpris {
  background-color: #242424;
  color: #FFFFFF;
}

.raven-mpris label {
  min-height: 24px;
}

.raven-mpris button.image-button {
  padding: 11px;
}

image.raven-mpris {
  background-color: rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.6);
  border-radius: 6px;
}

.raven-notifications-view > .raven-background > viewport.frame {
  padding: 0;
}

.raven-notifications-view > .raven-background > viewport.frame > list > row.activatable {
  margin-left: -6px;
  margin-right: -3px;
}

.raven-notifications-view > .raven-background > viewport.frame > list > row.activatable .raven-notifications-group-header {
  padding: 0 12px;
}

.raven-notifications-view > .raven-background > viewport.frame > list > row.activatable list {
  padding: 6px;
  background: none;
}

.raven-notifications-view > .raven-background > viewport.frame > list > row.activatable list > row.activatable {
  border: none;
  padding: 6px;
  padding-left: 12px;
  margin: 3px;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.04);
}

.raven-notifications-view > .raven-background > viewport.frame > list > row.activatable list > row.activatable:hover, .raven-notifications-view > .raven-background > viewport.frame > list > row.activatable list > row.activatable:selected {
  background-color: rgba(0, 0, 0, 0.12);
}

.raven-notifications-view > .raven-background > viewport.frame > list > row.activatable:selected, .raven-notifications-view > .raven-background > viewport.frame > list > row.activatable:selected:hover, .raven-notifications-view > .raven-background > viewport.frame > list > row.activatable:hover, .raven-notifications-view > .raven-background > viewport.frame > list > row.activatable:active, .raven-notifications-view > .raven-background > viewport.frame > list > row.activatable:focus {
  background: none;
  box-shadow: none;
}

.raven-notifications-group .raven-notifications-group-header {
  padding-left: 6px;
}

.raven-notifications-group list > row {
  border-radius: 6px;
  padding: 6px;
}

.raven-notifications-group list > row .notification-clone {
  padding-left: 6px;
}

.drop-shadow {
  margin: 12px;
  padding: 6px;
  border-radius: 12px;
  background-color: #FFFFFF;
  box-shadow: 0 2px 3px -1px rgba(0, 0, 0, 0.1), 0 4px 6px 0 rgba(0, 0, 0, 0.12), 0 1px 10px 0 rgba(0, 0, 0, 0.1), inset 0 0 0 1px rgba(255, 255, 255, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.12);
}

.budgie-notification-window, .budgie-switcher-window {
  background: none;
}

.budgie-notification .notification-title, .budgie-switcher .notification-title {
  font-size: 110%;
  color: rgba(0, 0, 0, 0.87);
}

.budgie-notification .notification-body, .budgie-switcher .notification-body {
  color: rgba(0, 0, 0, 0.6);
}

.budgie-osd-window {
  background: none;
}

.budgie-osd-window > box {
  border-radius: 12px;
  padding: 3px;
  margin: 0;
}

.budgie-osd {
  color: rgba(0, 0, 0, 0.87);
}

.budgie-osd .budgie-osd-text {
  font-size: 110%;
}

.budgie-switcher-window .drop-shadow {
  border-radius: 12px;
  padding: 0;
}

.budgie-switcher-window > box {
  padding: 6px;
  border-radius: 12px;
}

.budgie-switcher-window flowbox {
  color: rgba(0, 0, 0, 0.87);
  padding: 0;
}

.budgie-switcher-window flowboxchild {
  padding: 6px;
  margin: 0;
  color: rgba(0, 0, 0, 0.87);
  border-radius: 6px;
  transition: background-color 75ms ease-out;
}

.budgie-switcher-window flowboxchild:hover {
  color: rgba(0, 0, 0, 0.87);
  background-color: alpha(currentColor, 0.08);
}

.budgie-switcher-window flowboxchild:active {
  color: rgba(0, 0, 0, 0.87);
  background-color: alpha(currentColor, 0.12);
}

.budgie-switcher-window flowboxchild:selected {
  color: rgba(0, 0, 0, 0.87);
  background-color: alpha(currentColor, 0.1);
}

.budgie-panel .lock-keys image:disabled {
  color: rgba(255, 255, 255, 0.32);
}

.budgie-session-dialog,
.budgie-polkit-dialog,
.budgie-run-dialog {
  background-color: #FFFFFF;
  border: none;
  box-shadow: none;
  padding: 0;
}

.budgie-session-dialog > box > grid,
.budgie-polkit-dialog > box > grid,
.budgie-run-dialog > box > grid {
  padding: 24px;
}

.budgie-session-dialog.background, .budgie-session-dialog.background.csd > decoration,
.budgie-polkit-dialog.background,
.budgie-polkit-dialog.background.csd > decoration,
.budgie-run-dialog.background,
.budgie-run-dialog.background.csd > decoration {
  border-radius: 12px;
}

.budgie-session-dialog.background,
.budgie-polkit-dialog.background,
.budgie-run-dialog.background {
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15);
}

.budgie-session-dialog.background.csd > decoration,
.budgie-polkit-dialog.background.csd > decoration,
.budgie-run-dialog.background.csd > decoration {
  border: none;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 15px 16px 2px rgba(0, 0, 0, 0.14), 0 6px 18px 5px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.12);
}

.budgie-session-dialog label:not(:last-child),
.budgie-session-dialog .dialog-title,
.budgie-polkit-dialog label:not(:last-child),
.budgie-polkit-dialog .dialog-title,
.budgie-run-dialog label:not(:last-child),
.budgie-run-dialog .dialog-title {
  font-size: 110%;
}

.budgie-session-dialog buttonbox.linked > button,
.budgie-polkit-dialog buttonbox.linked > button,
.budgie-run-dialog buttonbox.linked > button {
  padding: 8px 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 0;
}

.budgie-session-dialog buttonbox.linked > button.suggested-action:not(:disabled),
.budgie-polkit-dialog buttonbox.linked > button.suggested-action:not(:disabled),
.budgie-run-dialog buttonbox.linked > button.suggested-action:not(:disabled) {
  color: @accent_color;
}

.budgie-session-dialog buttonbox.linked > button.destructive-action:not(:disabled),
.budgie-polkit-dialog buttonbox.linked > button.destructive-action:not(:disabled),
.budgie-run-dialog buttonbox.linked > button.destructive-action:not(:disabled) {
  color: #E53935;
}

.budgie-session-dialog buttonbox.linked > button:first-child,
.budgie-polkit-dialog buttonbox.linked > button:first-child,
.budgie-run-dialog buttonbox.linked > button:first-child {
  border-bottom-left-radius: 12px;
  border-top-left-radius: 0;
}

.budgie-session-dialog buttonbox.linked > button:last-child,
.budgie-polkit-dialog buttonbox.linked > button:last-child,
.budgie-run-dialog buttonbox.linked > button:last-child {
  border-bottom-right-radius: 12px;
  border-top-right-radius: 0;
}

.budgie-power-dialog .titlebar, .budgie-power-dialog .titlebar:backdrop {
  background-color: transparent;
  box-shadow: none;
  border: none;
}

.budgie-power-dialog.background {
  border-radius: 18px;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15);
}

.budgie-power-dialog.background.csd > decoration {
  border: none;
  border-radius: 18px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 15px 16px 2px rgba(0, 0, 0, 0.14), 0 6px 18px 5px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.12);
}

.budgie-polkit-dialog .message {
  color: rgba(0, 0, 0, 0.6);
}

.budgie-polkit-dialog .failure {
  color: #E53935;
}

.budgie-polkit-dialog > box > grid {
  padding-bottom: 0;
}

.budgie-run-dialog entry.search {
  font-size: 110%;
  padding: 6px 14px;
  border-image: none;
  box-shadow: none;
  background-color: transparent;
}

.budgie-run-dialog list .dim-label {
  color: rgba(0, 0, 0, 0.87);
}

.budgie-run-dialog scrolledwindow {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

/**************
 * Xfce4 Apps *
 **************/
.XfceHeading {
  background-color: #FFFFFF;
}

/***************
 * xfce4-panel *
 ***************/
.xfce4-panel.background {
  border: none;
  background-color: #212121;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.xfce4-panel.background button {
  min-height: 16px;
  min-width: 16px;
  padding: 0 6px;
  border-radius: 0;
  color: rgba(255, 255, 255, 0.7);
}

.xfce4-panel.background button:hover, .xfce4-panel.background button:active, .xfce4-panel.background button:checked {
  color: #FFFFFF;
}

.xfce4-panel.background button:disabled {
  color: rgba(255, 255, 255, 0.32);
}

.xfce4-panel.background button.flat.toggle {
  padding: 0 6px;
}

.xfce4-panel.background .tasklist button {
  padding: 0 6px;
}

.xfce4-panel.background .tasklist button image {
  padding: 4px;
}

wnck-pager:hover {
  background-color: alpha(currentColor, 0.08);
}

wnck-pager:active {
  background-color: alpha(currentColor, 0.12);
}

wnck-pager:selected {
  background-color: @accent_color;
}

#xfce4-mpc-plugin-26 > frame > border {
  border: none;
}

#xfce-panel-button {
  -gtk-icon-style: symbolic;
}

XfdesktopIconView.view {
  border-radius: 6px;
  background-color: transparent;
  color: #FFFFFF;
}

XfdesktopIconView.view:active {
  box-shadow: none;
}

XfdesktopIconView.view .rubberband {
  border-radius: 0;
}

window#whiskermenu-window {
  border-radius: 12px;
  background-color: transparent;
  border: none;
}

window#whiskermenu-window entry.search:focus {
  background-color: #FFFFFF;
}

window#whiskermenu-window > frame > border {
  border-radius: 12px;
  padding: 6px 8px 6px 9px;
  margin: 6px;
  border: none;
  background-color: #FFFFFF;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15), 0 3px 3px 0 rgba(0, 0, 0, 0.18), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.12), inset 0 0 0 1px rgba(255, 255, 255, 0.15);
}

window#whiskermenu-window box.categories > button.radio {
  padding: 3px 6px;
  margin: 1px 0;
}

window#whiskermenu-window box.categories > button.radio:hover {
  background-color: rgba(0, 0, 0, 0.12);
}

window#whiskermenu-window box.categories > button.radio:checked, window#whiskermenu-window box.categories > button.radio:active {
  background-color: rgba(0, 0, 0, 0.26);
  color: rgba(0, 0, 0, 0.87);
}

window#whiskermenu-window box.categories > button.radio:checked:hover, window#whiskermenu-window box.categories > button.radio:active:hover {
  background-image: none;
}

window#whiskermenu-window scrolledwindow.frame {
  padding: 3px;
  background-color: #FFFFFF;
  border-radius: 6px;
}

window#whiskermenu-window scrolledwindow.frame treeview.view {
  border-radius: 6px;
}

window#whiskermenu-window scrolledwindow.frame treeview.view:not(:hover):not(:selected) {
  background: none;
}

window#whiskermenu-window scrolledwindow.frame treeview.view:selected:hover {
  background-color: rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.87);
}

window#whiskermenu-window .title-area > .commands-area > button.flat.command-button:checked, window#whiskermenu-window .title-area > .commands-area > button.flat.command-button:active {
  background-color: rgba(0, 0, 0, 0.26);
  color: rgba(0, 0, 0, 0.87);
}

#XfceNotifyWindow {
  background-color: #FFFFFF;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15);
}

#XfceNotifyWindow buttonbox {
  padding: 0;
}

#XfceNotifyWindow label#summary {
  font-weight: bold;
}

dialog.xfsm-logout-dialog {
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.97);
  border: 1px solid rgba(0, 0, 0, 0.12);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15);
}

#xfwm-tabwin {
  padding: 12px;
  border-radius: 6px;
  -XfwmTabwinWidget-icon-size: 64px;
  -XfwmTabwinWidget-preview-size: 64px;
}

/**********
 * Thunar *
 **********/
.thunar toolbar {
  box-shadow: inset 0 -1px rgba(0, 0, 0, 0.12);
}

.thunar .standard-view.frame {
  border: none;
}

.thunar .standard-view.frame widget.view {
  border-radius: 6px;
}

.thunar .standard-view.frame .view:hover {
  color: rgba(0, 0, 0, 0.87);
}

.thunar .standard-view.frame .view:selected {
  color: #FFFFFF;
  background: @accent_color;
}

.thunar scrolledwindow.frame.sidebar {
  border-top: none;
}

.thunar .path-bar.linked:not(.vertical) > button.path-bar-button {
  margin-left: 2px;
  margin-right: 2px;
}

.thunar statusbar {
  margin: 0 -10px;
  padding: 0 4px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

.thunar > grid.horizontal > paned.horizontal > scrolledwindow.frame.sidebar.shortcuts-pane {
  border-top: none;
}

window.background.csd.thunar > grid.horizontal > paned.horizontal > scrolledwindow.frame.sidebar.shortcuts-pane {
  border-bottom-left-radius: 12px;
}

menubar.-vala-panel-appmenu-private,
menubar.-vala-panel-background {
  background: none;
  border: none;
  box-shadow: none;
  animation: none;
}

menubar.-vala-panel-appmenu-private > menuitem,
menubar.-vala-panel-background > menuitem {
  color: rgba(255, 255, 255, 0.7);
  font-weight: normal;
}

menubar.-vala-panel-appmenu-private > menuitem:hover,
menubar.-vala-panel-background > menuitem:hover {
  color: #FFFFFF;
  border-radius: 0;
}

menubar.-vala-panel-appmenu-private > menuitem:disabled,
menubar.-vala-panel-background > menuitem:disabled {
  color: rgba(255, 255, 255, 0.32);
}

/************************
 * LightDM GTK+ Greeter *
 ************************/
#panel_window {
  background-color: #2C2C2C;
  color: #FFFFFF;
}

#panel_window menubar,
#panel_window separator {
  background-color: transparent;
}

#panel_window separator {
  padding: 0 4px;
}

#panel_window separator:first-child {
  padding: 0 8px;
}

#panel_window menubar > menuitem {
  color: rgba(255, 255, 255, 0.7);
}

#panel_window menubar > menuitem:hover {
  color: #FFFFFF;
}

#panel_window menubar > menuitem:disabled label {
  color: rgba(255, 255, 255, 0.32);
}

#login_window,
#shutdown_dialog,
#restart_dialog {
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15), 0 3px 3px 0 rgba(0, 0, 0, 0.18), 0 3px 6px 0 rgba(0, 0, 0, 0.12), inset 0 1px rgba(255, 255, 255, 0.15);
  background-color: #FFFFFF;
  color: rgba(0, 0, 0, 0.87);
}

#content_frame {
  padding-bottom: 16px;
}

#login_window GtkComboBox {
  background: none;
}

#login_window GtkComboBox .button, #login_window GtkComboBox .button:hover, #login_window GtkComboBox .button:active, #login_window GtkComboBox .button:focus {
  padding: 0;
  background: none;
  border-style: none;
  box-shadow: none;
}

#user_image {
  padding: 3px;
  border-radius: 3px;
}

#user_image_border {
  border-radius: 3px;
}

#buttonbox_frame {
  padding-top: 24px;
}

#buttonbox_frame > box,
#buttonbox_frame > buttonbox {
  margin: -16px;
}

#buttonbox_frame button:not(:disabled) {
  color: @accent_color;
}

#greeter_infobar {
  font-weight: bold;
}

/********
 * Nemo *
 ********/
.nemo-window .primary-toolbar {
  background-color: #242424;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  padding: 2px 3px;
}

.nemo-window .primary-toolbar:backdrop {
  background-color: #2C2C2C;
}

.nemo-window .primary-toolbar entry {
  min-height: 0;
  margin: 0;
}

.nemo-window .primary-toolbar > toolitem > box > button.image-button,
.nemo-window .primary-toolbar > toolitem > .linked > button.image-button {
  margin-left: 1px;
  margin-right: 1px;
}

.nemo-window .primary-toolbar button.text-button {
  padding-left: 8px;
  padding-right: 8px;
  color: rgba(255, 255, 255, 0.7);
}

.nemo-window .primary-toolbar button.text-button:hover, .nemo-window .primary-toolbar button.text-button:active, .nemo-window .primary-toolbar button.text-button:checked {
  color: #FFFFFF;
}

.nemo-window .primary-toolbar button.text-button:disabled {
  color: rgba(255, 255, 255, 0.32);
}

.nemo-window .primary-toolbar button.text-button:backdrop {
  color: rgba(255, 255, 255, 0.5);
}

.nemo-window .primary-toolbar .path-bar.linked:not(.vertical) > button {
  margin-left: 1px;
  margin-right: 1px;
}

.nemo-window .primary-toolbar .path-bar.linked:not(.vertical) > button.slider-button {
  border-radius: 3px;
}

.nemo-window .primary-toolbar .path-bar.linked:not(.vertical) > button.slider-button:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.nemo-window .primary-toolbar .path-bar.linked:not(.vertical) > button.slider-button:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.nemo-window .primary-toolbar button:not(.text-button):not(.image-button) {
  padding-left: 4px;
  padding-right: 4px;
}

.nemo-window scrolledwindow.frame {
  border-style: none;
}

.nemo-window scrolledwindow.frame .view:not(:selected) {
  background-color: transparent;
}

.nemo-window .nemo-inactive-pane .view:not(:selected) {
  background-color: #FAFAFA;
}

.nemo-window .nemo-window-pane widget.entry {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.04);
}

.nemo-window .toolbar {
  padding: 2px;
  margin: -2px;
}

.nemo-window .toolbar button {
  margin: 3px 0;
  padding: 3px;
}

.nemo-window .toolbar separator {
  margin: 6px 0;
}

.nemo-window.background.csd .toolbar {
  border-radius: 0 0 12px 12px;
}

.nemo-window.background.csd.maximized .toolbar {
  border-radius: 0;
}

.places-treeview {
  -NemoPlacesTreeView-disk-full-bg-color: rgba(192, 192, 192, 0.961);
  -NemoPlacesTreeView-disk-full-fg-color: @accent_color;
  -NemoPlacesTreeView-disk-full-bar-width: 2px;
  -NemoPlacesTreeView-disk-full-bar-radius: 0;
  -NemoPlacesTreeView-disk-full-bottom-padding: 1px;
  -NemoPlacesTreeView-disk-full-max-length: 80px;
  padding-top: 3px;
  padding-bottom: 3px;
}

/* GTK NAMED COLORS
   ----------------
   use responsibly! */
/*
widget text/foreground color */
@define-color theme_fg_color rgba(0, 0, 0, 0.87);
/*
text color for entries, views and content in general */
@define-color theme_text_color rgba(0, 0, 0, 0.87);
/*
widget base background color */
@define-color theme_bg_color #FFFFFF;
/*
text widgets and the like base background color */
@define-color theme_base_color #FFFFFF;
/*
base background color of selections */
@define-color theme_selected_bg_color @accent_color;
/*
text/foreground color of selections */
@define-color theme_selected_fg_color #FFFFFF;
/*
base background color of insensitive widgets */
@define-color insensitive_bg_color #FFFFFF;
/*
text foreground color of insensitive widgets */
@define-color insensitive_fg_color rgba(0, 0, 0, 0.38);
/*
insensitive text widgets and the like base background color */
@define-color insensitive_base_color #FAFAFA;
/*
widget text/foreground color on backdrop windows */
@define-color theme_unfocused_fg_color rgba(0, 0, 0, 0.87);
/*
text color for entries, views and content in general on backdrop windows */
@define-color theme_unfocused_text_color rgba(0, 0, 0, 0.87);
/*
widget base background color on backdrop windows */
@define-color theme_unfocused_bg_color #FFFFFF;
/*
text widgets and the like base background color on backdrop windows */
@define-color theme_unfocused_base_color #FFFFFF;
/*
base background color of selections on backdrop windows */
@define-color theme_unfocused_selected_bg_color @accent_color;
/*
text/foreground color of selections on backdrop windows */
@define-color theme_unfocused_selected_fg_color #FFFFFF;
/*
insensitive color on backdrop windows */
@define-color unfocused_insensitive_color rgba(0, 0, 0, 0.38);
/*
widgets main borders color */
@define-color borders rgba(0, 0, 0, 0.12);
/*
widgets main borders color on backdrop windows */
@define-color unfocused_borders rgba(0, 0, 0, 0.12);
/*
these are pretty self explicative */
@define-color warning_color #FBC02D;
@define-color error_color #E53935;
@define-color success_color #4CAF50;
/*
these colors are exported for the window manager and shouldn't be used in applications,
read if you used those and something break with a version upgrade you're on your own... */
@define-color wm_title #FFFFFF;
@define-color wm_unfocused_title rgba(255, 255, 255, 0.7);
@define-color wm_highlight rgba(255, 255, 255, 0.1);
@define-color wm_border #a6a6a6;
@define-color wm_bg #242424;
@define-color wm_unfocused_bg #2C2C2C;
@define-color wm_button_icon white;
@define-color wm_button_close_hover_bg #fd5f51;
@define-color wm_button_close_active_bg #fc2714;
@define-color wm_button_max_hover_bg #38c76a;
@define-color wm_button_max_active_bg #2b9751;
@define-color wm_button_min_hover_bg #fdbe04;
@define-color wm_button_min_active_bg #c29102;
/*
FIXME this is really an API */
@define-color content_view_bg #FFFFFF;
@define-color placeholder_text_color #666666;
/* Very contrasty background for text views (@theme_text_color foreground) */
@define-color text_view_bg #FFFFFF;
@define-color budgie_tasklist_indicator_color rgba(255, 255, 255, 0.3);
@define-color budgie_tasklist_indicator_color_active @accent_color;
@define-color budgie_tasklist_indicator_color_active_window #305695;
@define-color budgie_tasklist_indicator_color_attention #FBC02D;
@define-color STRAWBERRY_100 #FF9262;
@define-color STRAWBERRY_300 #FF793E;
@define-color STRAWBERRY_500 #F15D22;
@define-color STRAWBERRY_700 #CF3B00;
@define-color STRAWBERRY_900 #AC1800;
@define-color ORANGE_100 #FFDB91;
@define-color ORANGE_300 #FFCA40;
@define-color ORANGE_500 #FAA41A;
@define-color ORANGE_700 #DE8800;
@define-color ORANGE_900 #C26C00;
@define-color BANANA_100 #FFFFA8;
@define-color BANANA_300 #FFFA7D;
@define-color BANANA_500 #FFCE51;
@define-color BANANA_700 #D1A023;
@define-color BANANA_900 #A27100;
@define-color LIME_100 #A2F3BE;
@define-color LIME_300 #8ADBA6;
@define-color LIME_500 #73C48F;
@define-color LIME_700 #479863;
@define-color LIME_900 #1C6D38;
@define-color BLUEBERRY_100 #94A6FF;
@define-color BLUEBERRY_300 #6A7CE0;
@define-color BLUEBERRY_500 #3F51B5;
@define-color BLUEBERRY_700 #213397;
@define-color BLUEBERRY_900 #031579;
@define-color GRAPE_100 #D25DE6;
@define-color GRAPE_300 #B84ACB;
@define-color GRAPE_500 #9C27B0;
@define-color GRAPE_700 #830E97;
@define-color GRAPE_900 #6A007E;
@define-color COCOA_100 #9F9792;
@define-color COCOA_300 #7B736E;
@define-color COCOA_500 #574F4A;
@define-color COCOA_700 #463E39;
@define-color COCOA_900 #342C27;
@define-color SILVER_100 #EEE;
@define-color SILVER_300 #CCC;
@define-color SILVER_500 #AAA;
@define-color SILVER_700 #888;
@define-color SILVER_900 #666;
@define-color SLATE_100 #888;
@define-color SLATE_300 #666;
@define-color SLATE_500 #444;
@define-color SLATE_700 #222;
@define-color SLATE_900 #111;
@define-color BLACK_100 #474341;
@define-color BLACK_300 #403C3A;
@define-color BLACK_500 #393634;
@define-color BLACK_700 #33302F;
@define-color BLACK_900 #2B2928;
