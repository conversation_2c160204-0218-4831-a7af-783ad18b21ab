// 🚨 CRITICAL UPDATE - AMERICA'S KEYBOARD CRISIS RESOLVED! 🇺🇸
// Smart scaling and space bar now working!
import QtQuick
import Quickshell

ShellRoot {
    id: root

    Rectangle {
        width: 1400
        height: 900
        color: "#000011"

        Column {
            anchors.centerIn: parent
            spacing: 25

            Text {
                text: "🚨 CAPS LOCK + HEIGHT SCALING FIXED! 🇺🇸"
                font.pixelSize: 54
                font.bold: true
                color: "#FF0000"
                anchors.horizontalCenter: parent.horizontalCenter
            }

            Text {
                text: "CAPS LOCK BUTTON + IMPLICIT HEIGHT! ✅"
                font.pixelSize: 42
                font.bold: true
                color: "#00FF00"
                anchors.horizontalCenter: parent.horizontalCenter
            }

            Rectangle {
                width: 1000
                height: 4
                color: "#FF0000"
                anchors.horizontalCenter: parent.horizontalCenter
            }

            Grid {
                columns: 2
                spacing: 30
                anchors.horizontalCenter: parent.horizontalCenter

                Rectangle {
                    width: 450
                    height: 160
                    color: "#1a1a1a"
                    border.color: "#FF6600"
                    border.width: 4
                    radius: 12

                    Column {
                        anchors.centerIn: parent
                        spacing: 12

                        Text {
                            text: "🔄 FOUR-CORNER RESIZE"
                            font.pixelSize: 20
                            font.bold: true
                            color: "#FF6600"
                            anchors.horizontalCenter: parent.horizontalCenter
                        }

                        Text {
                            text: "• Top-left, top-right handles\n• Bottom-left, bottom-right handles\n• All corners resize independently"
                            font.pixelSize: 14
                            color: "white"
                            horizontalAlignment: Text.AlignHCenter
                        }
                    }
                }

                Rectangle {
                    width: 450
                    height: 160
                    color: "#1a1a1a"
                    border.color: "#FF00FF"
                    border.width: 4
                    radius: 12

                    Column {
                        anchors.centerIn: parent
                        spacing: 12

                        Text {
                            text: "🔒 CAPS LOCK RESTORED"
                            font.pixelSize: 20
                            font.bold: true
                            color: "#FF00FF"
                            anchors.horizontalCenter: parent.horizontalCenter
                        }

                        Text {
                            text: "• Caps Lock button added to layout\n• keytype: 'toggle', shape: 'caps'\n• Proper keyboard functionality restored!"
                            font.pixelSize: 14
                            color: "white"
                            horizontalAlignment: Text.AlignHCenter
                        }
                    }
                }

                Rectangle {
                    width: 420
                    height: 140
                    color: "#1a1a1a"
                    border.color: "#00FF00"
                    border.width: 3
                    radius: 10

                    Column {
                        anchors.centerIn: parent
                        spacing: 10

                        Text {
                            text: "✅ RESIZING WORKS"
                            font.pixelSize: 18
                            font.bold: true
                            color: "#00FF00"
                            anchors.horizontalCenter: parent.horizontalCenter
                        }

                        Text {
                            text: "• Bottom-right resize handle\n• Size limits enforced\n• Smooth resize operation"
                            font.pixelSize: 14
                            color: "white"
                            horizontalAlignment: Text.AlignHCenter
                        }
                    }
                }

                Rectangle {
                    width: 420
                    height: 140
                    color: "#1a1a1a"
                    border.color: "#00FF00"
                    border.width: 3
                    radius: 10

                    Column {
                        anchors.centerIn: parent
                        spacing: 10

                        Text {
                            text: "✅ SPACING FIXED"
                            font.pixelSize: 18
                            font.bold: true
                            color: "#00FF00"
                            anchors.horizontalCenter: parent.horizontalCenter
                        }

                        Text {
                            text: "• Bottom margin: 45px\n• Proper button spacing\n• No edge sticking"
                            font.pixelSize: 14
                            color: "white"
                            horizontalAlignment: Text.AlignHCenter
                        }
                    }
                }
            }

            Rectangle {
                width: 1000
                height: 140
                color: "#FF0000"
                border.color: "#FFFFFF"
                border.width: 5
                radius: 25
                anchors.horizontalCenter: parent.horizontalCenter

                Text {
                    anchors.centerIn: parent
                    text: "🎯 ULTIMATE VICTORY ACHIEVED! 🎯\n🚨 implicitHeight forces button height scaling!\n✅ Caps Lock button restored to keyboard\n✅ Layout.maximumHeight prevents overflow\n🇺🇸 AMERICA IS ABSOLUTELY PERFECT! 🇺🇸"
                    color: "white"
                    font.pixelSize: 18
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                }
            }
        }
    }
}
