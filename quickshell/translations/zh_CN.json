{"Mo": "一/*keep*/", "Tu": "二/*keep*/", "We": "三/*keep*/", "Th": "四/*keep*/", "Fr": "五/*keep*/", "Sa": "六/*keep*/", "Su": "日/*keep*/", "%1 characters": "%1 个字符", "**Pricing**: free. Data used for training.\n\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key": "**价格**：免费。数据用于训练。\n\n**说明**：登录 Google 账户，允许 AI Studio 创建 Google Cloud 项目或其他要求，然后返回并点击获取 API 密钥", "**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\n\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key": "**价格**：免费。数据使用政策取决于您的 OpenRouter 账户设置。\n\n**说明**：登录 OpenRouter 账户，在右上角菜单中选择 Keys，点击创建 API 密钥", ". Notes for Zerochan:\n- You must enter a color\n- Set your zerochan username in `sidebar.booru.zerochan.username` config option. You [might be banned for not doing so](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!": ". Notes for Zerochan:\n- You must enter a color\n- Set your zerochan username in `sidebar.booru.zerochan.username` config option. You [might be banned for not doing so](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!", "<i>No further instruction provided</i>": "<i>未提供进一步说明</i>", "API key set for %1": "已为 %1 设置 API 密钥", "API key:\n\n```txt\n%1\n```": "API 密钥：\n\n```txt\n%1\n```", "Action": "操作", "Add": "添加", "Add task": "添加任务", "All-rounder | Good quality, decent quantity": "全能型 | 质量好，数量适中", "Allow NSFW": "允许 NSFW", "Allow NSFW content": "允许 NSFW 内容", "Anime": "动漫", "Anime boorus": "动漫图库", "App": "应用", "Arrow keys to navigate, Enter to select\nEsc or click anywhere to cancel": "方向键导航，回车选择\nEsc 或点击任意地方取消", "Bluetooth": "蓝牙", "Brightness": "亮度", "Cancel": "取消", "Cheat sheet": "快捷键表", "Choose model": "选择模型", "Clean stuff | Excellent quality, no NSFW": "清洁内容 | 优秀质量，无 NSFW", "Clear": "清除", "Clear chat history": "清除聊天记录", "Clear the current list of images": "清除当前图片列表", "Close": "关闭", "Copy": "复制", "Copy code": "复制代码", "Current API endpoint: %1\nSet it with %2mode PROVIDER": "当前 API 端点：%1\n使用 %2mode PROVIDER 设置", "Delete": "删除", "Desktop": "桌面", "Disable NSFW content": "禁用 NSFW 内容", "Done": "完成", "Download": "下载", "Edit": "编辑", "Enter text to translate...": "输入要翻译的文本...", "Finished tasks will go here": "已完成的任务将显示在这里", "For desktop wallpapers | Good quality": "桌面壁纸专用 | 质量好", "For storing API keys and other sensitive information": "用于存储 API 密钥和其他敏感信息", "Game mode": "游戏模式", "Get the next page of results": "获取下一页结果", "Go to source (%1)": "转到源 (%1)", "Hibernate": "休眠", "Input": "输入", "Intelligence": "智能体", "Interface": "界面", "Invalid arguments. Must provide `key` and `value`.": "参数无效。必须提供 `key` 和 `value`。", "Jump to current month": "跳转到当前月份", "Keep system awake": "保持系统唤醒", "Large images | God tier quality, no NSFW.": "大尺寸图片 | 顶级质量，无 NSFW", "Large language models": "大语言模型", "Launch": "启动", "Local Ollama model | %1": "本地 Ollama 模型 | %1", "Lock": "锁定", "Logout": "注销", "Markdown test": "Markdown 测试", "Math result": "数学结果", "No API key set for %1": "未为 %1 设置 API 密钥", "No audio source": "无音频源", "No media": "无媒体", "No notifications": "无通知", "Not visible to model": "对模型不可见", "Nothing here!": "这里什么都没有！", "Notifications": "通知", "OK": "确定", "Open file link": "打开文件链接", "Output": "输出", "Page %1": "第 %1 页", "Reboot": "重启", "Reboot to firmware settings": "重启到固件设置", "Reload Hyprland & Quickshell": "重新加载 Hyprland 和 Quickshell", "Run": "运行", "Run command": "运行命令", "Save": "保存", "Save to Downloads": "保存到下载文件夹", "Search": "搜索", "Search the web": "在网络上搜索", "Search, calculate or run": "搜索、计算或运行", "Select Language": "选择语言", "Session": "会话", "Set API key": "设置 API 密钥", "Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.": "设置模型的温度（随机性）。Gemini 模型范围为 0 到 2，其他模型为 0 到 1。默认值为 0.5。", "Set the current API provider": "设置当前 API 提供商", "Shutdown": "关机", "Silent": "静音", "Sleep": "睡眠", "System": "系统", "Task Manager": "任务管理器", "Task description": "任务描述", "Temperature must be between 0 and 2": "温度必须在 0 到 2 之间", "Temperature set to %1": "温度设置为 %1", "Temperature: %1": "温度：%1", "The hentai one | Great quantity, a lot of NSFW, quality varies wildly": "成人向 | 数量巨大，大量 NSFW，质量参差不齐", "The popular one | Best quantity, but quality can vary wildly": "最受欢迎 | 数量最多，但质量参差不齐", "Thinking": "思考中", "Translation goes here...": "翻译结果会显示在这里...", "Translator": "翻译器", "Unfinished": "未完成", "Unknown": "未知", "Unknown Album": "未知专辑", "Unknown Artist": "未知艺术家", "Unknown Title": "未知标题", "Unknown function call: %1": "未知函数调用：%1", "View Markdown source": "查看 Markdown 源码", "Volume": "音量", "Volume mixer": "音量混合器", "Waifus only | Excellent quality, limited quantity": "仅限角色 | 优秀质量，数量有限", "Waiting for response...": "等待响应...", "Workspace": "工作区", "%1 Safe Storage": "%1 安全存储", "%1 does not require an API key": "%1 不需要 API 密钥", "%1 queries pending": "%1 个查询等待中", "%1 | Right-click to configure": "%1 | 右键点击进行配置", "Invalid API provider. Supported: \n-": "无效的 API 提供商。支持的：\n-", "Unknown command:": "未知命令：", "Type /key to get started with online models\nCtrl+O to expand the sidebar\nCtrl+P to detach sidebar into a window": "输入 /key 开始使用在线模型\nCtrl+O 展开侧边栏\nCtrl+P 将侧边栏分离为窗口", "Provider set to": "提供商设置为", "Invalid model. Supported: \n```": "无效模型。支持的：\n```", "Switched to search mode. Continue with the user's request.": "已切换到搜索模式。继续处理用户请求。", "Enter tags, or \"%1\" for commands": "输入标签，或 \"%1\" 查看命令", "Online via %1 | %2's model": "通过 %1 在线 | %2 的模型", "That didn't work. Tips:\n- Check your tags and NSFW settings\n- If you don't have a tag in mind, type a page number": "没有找到结果。提示：\n- 检查您的标签和 NSFW 设置\n- 如果没有想到标签，请输入页码", "Settings": "设置", "Save chat": "保存对话", "Load chat": "加载对话", "or": "或", "Set the system prompt for the model.": "为模型设置系统提示。", "To Do": "待办", "Calendar": "日历", "Advanced": "高级", "About": "关于", "Services": "服务", "Style": "样式", "Edit config": "编辑配置", "Colors & Wallpaper": "颜色和壁纸", "Light": "浅色", "Dark": "深色", "Material palette": "颜色主题", "Fidelity": "保真度", "Fruit Salad": "水果沙拉", "Alternatively use /dark, /light, /img in the launcher": "或者在启动器中使用 /dark、/light、/img", "Fake screen rounding": "伪造屏幕圆角", "When not fullscreen": "非全屏时", "Choose file": "选择文件", "Random SFW Anime wallpaper from Konachan\nImage is saved to ~/Pictures/Wallpapers": "随机 Konachan SFW 动漫壁纸\n图片保存到 ~/图片/Wallpapers", "Be patient...": "请耐心等待...", "Decorations & Effects": "装饰与特效", "Tonal Spot": "色调点", "Shell windows": "Shell 窗口", "Auto": "自动", "Wallpaper": "壁纸", "Content": "内容", "Title bar": "标题栏", "Transparency": "透明度", "Expressive": "表现力", "Yes": "是", "Enable": "启用", "Rainbow": "彩虹", "Might look ass. Unsupported.": "可能效果很差。不支持。", "Monochrome": "单色", "Random: Konachan": "随机：Konachan", "Center title": "标题居中", "Neutral": "中性", "Pick wallpaper image on your system": "在系统中选择壁纸图片", "No": "否", "AI": "AI", "Local only": "仅本地", "Policies": "策略", "Weeb": "二次元", "Closet": "隐藏", "Bar style": "条栏样式", "Show next time": "下次显示", "Usage": "用法", "Plain rectangle": "纯矩形", "Useless buttons": "无用按钮", "GitHub": "GitHub", "Style & wallpaper": "样式与壁纸", "Configuration": "配置", "Change any time later with /dark, /light, /img in the launcher": "之后可在启动器用 /dark、/light、/img 更改", "Keybinds": "快捷键", "Float": "浮动", "Hug": "贴合", "Yooooo hi there": "哟嗬，您好呀", "illogical-impulse Welcome": "illogical-impulse 欢迎页", "Info": "信息", "Volume limit": "音量限制", "Prevents abrupt increments and restricts volume limit": "防止骤增并限制音量", "Resources": "资源", "12h am/pm": "12小时 上午/下午", "Base URL": "基础 URL", "Audio": "声音", "Networking": "网络", "Format": "格式", "Time": "时间", "Battery": "电池", "Prefixes": "前缀", "Emojis": "表情符号", "Earbang protection": "防爆音保护", "Automatically suspends the system when battery is low": "电池电量低时自动挂起系统", "Automatic suspend": "自动挂起", "Suspend at": "挂起阈值", "Max allowed increase": "最大允许增幅", "Web search": "网页搜索", "Polling interval (ms)": "轮询间隔（毫秒）", "Clipboard": "剪贴板", "Low warning": "低电量警告", "24h": "24小时制", "Use Levenshtein distance-based algorithm instead of fuzzy": "使用 Levenshtein 距离算法替代模糊匹配", "System prompt": "系统提示词", "12h AM/PM": "12小时 AM/PM", "Could be better if you make a ton of typos,\nbut results can be weird and might not work with acronyms\n(e.g. \"GIMP\" might not give you the paint program)": "如果你经常打错字可能更好用，但结果可能很奇怪，并且可能无法匹配缩写（如 \"GIMP\" 可能搜不到绘图程序）", "Critical warning": "临界警告", "User agent (for services that require it)": "用户代理（部分服务需要）", "Such regions could be images or parts of the screen that have some containment.\nMight not always be accurate.\nThis is done with an image processing algorithm run locally and no AI is used.": "这些区域可能是图片或屏幕中具有一定包容性的部分。\n可能并不总是准确。\n这是通过本地运行的图像处理算法实现的，没有使用 AI。", "Note: turning off can hurt readability": "注意：关闭后可能影响可读性", "Workspaces shown": "显示的工作区数", "Dark/Light toggle": "深浅色切换", "Dock": "停靠栏", "Weather": "天气", "Pinned on startup": "启动时固定", "Tip: Hide icons and always show numbers for\nthe classic illogical-impulse experience": "提示：隐藏图标并始终显示数字以获得经典体验", "Always show numbers": "总是显示数字", "Buttons": "按钮", "Keyboard toggle": "键盘切换", "Scale (%)": "缩放比例(%)", "Overview": "概览", "Rows": "行数", "Borderless": "无边框", "Screenshot tool": "截图工具", "Number show delay when pressing Super (ms)": "按下 Super 时数字显示延迟(ms)", "Timeout (ms)": "超时时间(ms)", "Show app icons": "显示应用图标", "Workspaces": "工作区", "Columns": "列数", "On-screen display": "屏幕显示", "Screen snip": "屏幕截图", "Mic toggle": "麦克风切换", "Hover to reveal": "悬停显示", "Bar": "条栏", "Show background": "显示背景", "Show regions of potential interest": "显示可能感兴趣的区域", "Color picker": "取色器", "Help & Support": "帮助与支持", "Discussions": "讨论区", "Color generation": "配色生成", "Dotfiles": "配置文件", "Distro": "发行版", "Privacy Policy": "隐私政策", "Documentation": "文档", "Shell & utilities theming must also be enabled": "必须同时启用 Shell 与工具主题", "illogical-impulse": "illogical-impulse", "Donate": "捐助", "Terminal": "终端", "Shell & utilities": "Shell 与工具", "Qt apps": "Qt 应用", "Report a Bug": "报告问题", "Issues": "问题追踪", "Drag or click a region • LMB: Copy • RMB: Edit": "拖动或点击一个区域 • 鼠标左键：复制 • 鼠标右键：编辑", "Current model: %1\nSet it with %2model MODEL": "当前模型：%1\n使用 %2model MODEL 设置", "Message the model... \"%1\" for commands": "与模型对话... \"%1\" 查看命令", "The current system prompt is\n\n---\n\n%1": "当前系统提示词为\n\n---\n\n%1", "Model set to %1": "模型已设置为 %1", "Loaded the following system prompt\n\n---\n\n%1": "已加载以下系统提示词\n\n---\n\n%1", "%1 notifications": "%1 条通知", "Save chat to %1": "保存聊天记录到 %1", "Load chat from %1": "从 %1 加载聊天记录", "Load prompt from %1": "从 %1 加载提示词", "Select output device": "选择输出设备", "%1   •   %2 tasks": "%1   •   %2 个任务", "Online models disallowed\n\nControlled by `policies.ai` config option": "禁止在线模型\n\n由 `policies.ai` 配置项控制", "Select input device": "选择输入设备", "Low battery": "电量低", "Registration failed. Please inspect manually with the <tt>warp-cli</tt> command": "注册失败。请使用 <tt>warp-cli</tt> 命令手动检查", "Code saved to file": "代码已保存到文件", "Consider plugging in your device": "请考虑连接您的设备", "Weather Service": "天气服务", "Please charge!\nAutomatic suspend triggers at %1": "请充电！\n自动挂起将在 %1 时触发", "Cloudflare WARP (1.1.1.1)": "Cloudflare WARP (1.1.1.1)", "Cloudflare WARP": "Cloudflare WARP", "Download complete": "下载完成", "Critically low battery": "电量极低", "Scroll to change brightness": "滚动以调节亮度", "Saved to %1": "已保存到 %1", "Cannot find a GPS service. Using the fallback method instead.": "无法找到 GPS 服务。正在使用备用方法。", "Elements": "元素", "Scroll to change volume": "滚动以调节音量", "Connection failed. Please inspect manually with the <tt>warp-cli</tt> command": "连接失败。请使用 <tt>warp-cli</tt> 命令手动检查", "UV Index": "紫外线指数", "Pressure": "气压", "Visibility": "能见度", "Sunrise": "日出", "Sunset": "日落", "Humidity": "湿度", "Wind": "风", "Precipitation": "降水量", "Time to full:": "距离充满：", "Time to empty:": "距离耗尽：", "Fully charged": "已充满电", "Charging:": "充电功率：", "Discharging:": "放电功率：", "Uptime:": "运行时间：", "Upcoming Tasks:": "待办任务：", "No pending tasks": "没有待办任务", "... and %1 more": "... 还有 %1 个", "Memory Usage": "内存使用情况", "Used:": "已用：", "Free:": "可用：", "Total:": "总计：", "Usage:": "占比：", "Swap Usage": "交换区使用情况", "Swap:": "交换区：", "Not configured": "未配置", "CPU Usage": "CPU 使用情况", "Current:": "当前占比：", "Load:": "负载：", "High": "高", "Medium": "中", "Low": "低", "System Resource": "系统资源", "Tint icons": "图标着色", "Performance Profile toggle": "性能配置文件切换", "**Instructions**: Log into Mistral account, go to Keys on the sidebar, click Create new key": "**说明**：登录 Mistral 账户，在侧边栏中选择 Keys，点击创建新密钥", "Invalid arguments. Must provide `command`.": "参数无效。必须提供 `command`。", "Thought": "思考", "Online | Google's model\nA Gemini 2.5 Flash model optimized for cost-efficiency and high throughput.": "在线 | Google 模型\n针对成本效益和高吞吐量优化的 Gemini 2.5 Flash 模型。", "Online | Google's model\nFast, can perform searches for up-to-date information": "在线 | Google 模型\n速度快，可搜索最新信息", "Your package manager is running": "您的包管理器正在运行", "Gives the model search capabilities (immediately)": "为模型提供搜索功能（即时）", "Set the tool to use for the model.": "设置模型使用的工具。", "Night Light | Right-click to toggle Auto mode": "夜间模式 | 右键切换自动模式", "Online | %1's model | Delivers fast, responsive and well-formatted answers. Disadvantages: not very eager to do stuff; might make up unknown function calls": "在线 | %1 的模型 | 提供快速、响应迅速且格式良好的答案。缺点：不太积极主动；可能编造未知的函数调用", "Depends on workspace": "取决于工作区", "Usage: %1tool TOOL_NAME": "用法：%1tool 工具名称", "Tray": "托盘", "Usage: %1save CHAT_NAME": "用法：%1save 聊天名称", "Approve": "批准", "Depends on sidebars": "取决于侧边栏", "Commands, edit configs, search.\nTakes an extra turn to switch to search mode if that's needed": "命令、编辑配置、搜索。\n如果需要，会额外执行一次切换到搜索模式", "Overall appearance": "整体外观", "Up %1": "运行 %1", "Tool set to: %1": "工具设置为：%1", "Wallpaper parallax": "壁纸视差", "Online | Google's model\nGoogle's state-of-the-art multipurpose model that excels at coding and complex reasoning tasks.": "在线 | Google 模型\nGoogle 最先进的多用途模型，在编程和复杂推理任务方面表现卓越。", "When enabled keeps the content of the right sidebar loaded to reduce the delay when opening,\nat the cost of around 15MB of consistent RAM usage. Delay significance depends on your system's performance.\nUsing a different kernel might help with this delay": "启用时会保持右侧边栏内容加载以减少打开延迟，\n代价是持续使用约 15MB 内存。延迟程度取决于您的系统性能。\n使用不同的内核可能有助于减少延迟", "Tint app icons": "应用图标着色", "Preferred wallpaper zoom (%)": "首选壁纸缩放比例 (%)", "To set an API key, pass it with the %4 command\n\nTo view the key, pass \"get\" with the command<br/>\n\n### For %1:\n\n**Link**: %2\n\n%3": "要设置 API 密钥，请使用 %4 命令传递\n\n要查看密钥，请在命令中传递 \"get\"<br/>\n\n### 对于 %1：\n\n**链接**：%2\n\n%3", "No API key\nSet it with /key YOUR_API_KEY": "无 API 密钥\n使用 /key YOUR_API_KEY 设置", "Total token count\nInput: %1\nOutput: %2": "总令牌数\n输入：%1\n输出：%2", "Disable tools": "禁用工具", "API key is set\nChange with /key YOUR_API_KEY": "API 密钥已设置\n使用 /key YOUR_API_KEY 更改", "Usage: %1load CHAT_NAME": "用法：%1load 聊天名称", "Sidebars": "侧边栏", "Temperature\nChange with /temp VALUE": "温度\n使用 /temp VALUE 更改", "Current tool: %1\nSet it with %2tool TOOL": "当前工具：%1\n使用 %2tool TOOL 设置", "There might be a download in progress": "可能有下载正在进行", "Online | Google's model\nNewer model that's slower than its predecessor but should deliver higher quality answers": "在线 | Google 模型\n比前代模型更慢但应该提供更高质量答案的新模型", "EasyEffects | Right-click to configure": "EasyEffects | 右键配置", "Command rejected by user": "用户拒绝了命令", "Invalid tool. Supported tools:\n- %1": "无效工具。支持的工具：\n- %1", "Keep right sidebar loaded": "保持右侧边栏加载", "Reject": "拒绝"}