{"Unknown function call: %1": "<PERSON><PERSON><PERSON> k<PERSON>ng x<PERSON>c đ<PERSON>: %1", "Show next time": "<PERSON><PERSON><PERSON> thị lần sau", "Fidelity": "<PERSON>há giống gốc", "Open file link": "Mở liên kết tệp", "Interrupts possibility of overview being toggled on release.": "Ngăn mở overview khi nhả nút.", "No audio source": "<PERSON><PERSON><PERSON><PERSON> có nguồn âm thanh", "Might look ass. Unsupported.": "<PERSON><PERSON> thể rất tệ. <PERSON><PERSON><PERSON><PERSON> được hỗ trợ.", "Jump to current month": "<PERSON><PERSON><PERSON><PERSON> đến tháng hiện tại", "Delete": "Xóa", "**Pricing**: free. Data used for training.\n\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key": "**Giá**: miễn phí. <PERSON><PERSON> liệu được sử dụng cho mục đích huấn luyện. **Hướng dẫn**: <PERSON><PERSON><PERSON> nhập vào tài k<PERSON>n Google, cho phép AI Studio tạo dự án Google Cloud gì đó, quay lại rồi ấn Get API key", "Rainbow": "<PERSON><PERSON><PERSON> vồng", "%1 does not require an API key": "%1 không cần API key", "Choose model": "<PERSON><PERSON><PERSON> model", "Prevents abrupt increments and restricts volume limit": "Chặn thay đổi đột ngột và giới hạn âm lượng", "%1 characters": "%1 ký tự", "Change any time later with /dark, /light, /img in the launcher": "<PERSON>hay đ<PERSON>i bất cứ lúc nào sau này với /dark, /light, /img trong launcher", "Tonal Spot": "Tonal Spot", "Neutral": "Trung tính", "To Do": "<PERSON><PERSON><PERSON>", "Auto": "<PERSON><PERSON> động", "Polling interval (ms)": "<PERSON><PERSON><PERSON><PERSON> gian lặp lại (ms)", "Center title": "<PERSON><PERSON><PERSON> gi<PERSON>a tiêu đề", "Lock": "<PERSON><PERSON><PERSON><PERSON> màn hình", "Screen snip": "<PERSON><PERSON><PERSON> màn hình (chọn vùng)", "User agent (for services that require it)": "User agent (n<PERSON><PERSON> c<PERSON>n)", "Report a Bug": "Báo lỗi", "Shutdown": "<PERSON><PERSON><PERSON>", "Keyboard toggle": "Mở/đóng bàn phím ảo", "The hentai one | Great quantity, a lot of NSFW, quality varies wildly": "<PERSON><PERSON>i nhiều hentai nhất | <PERSON><PERSON> lượng rất tốt, rất nhiều NSFW, chất lượng có thể khác nhau nhiều", "Download": "<PERSON><PERSON><PERSON>", "Note: turning off can hurt readability": "<PERSON>hi chú: nếu tắt có thể khó đọc", "Local Ollama model | %1": "Model Ollama trên m<PERSON> | %1", "Silent": "Im lặng", "Columns": "Số cột", "Set with /mode PROVIDER": "Set with /mode PROVIDER", "Issues": "<PERSON><PERSON><PERSON> vấn đề", "Policies": "<PERSON><PERSON><PERSON>", "Load chat from %1": "<PERSON><PERSON><PERSON> trò chuyện từ %1", "Unknown Album": "Album không x<PERSON>c đ<PERSON>", "Yes": "<PERSON><PERSON>", "Battery": "<PERSON>n", "Material palette": "Kiểu material", "Chain of Thought": "Dòng suy nghĩ", "This is necessary because GlobalShortcut.onReleased in quickshell triggers whether or not you press something else while holding the key.": "Cần cái này vì GlobalShortcut.onReleased cho một phím của Quickshell được kích hoạt kể cả khi ban ân phím khác trước khi thả phím đó.", "Low warning": "<PERSON><PERSON><PERSON> b<PERSON>o thấp", ". Notes for Zerochan:\n- You must enter a color\n- Set your zerochan username in `sidebar.booru.zerochan.username` config option. You [might be banned for not doing so](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!": ". Với Zerochan:\n- <PERSON><PERSON><PERSON> <PERSON>hậ<PERSON> tên một màu (bằng tiếng <PERSON>)\n- Đặt username Zerochan trong tùy chọn `sidebar.booru.zerochan.username`. Bạn [có thể bị ban nếu không tuân thủ](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!", "Brightness": "<PERSON><PERSON> sáng", "Yooooo hi there": "Yooooo ch<PERSON>o b<PERSON>n", "Colors & Wallpaper": "Màu sắ<PERSON> & Hình <PERSON>ền", "No media": "Không có media", "Critical warning": "<PERSON><PERSON><PERSON> b<PERSON>o rất thấp", "Mic toggle": "Bật/tắt mic", "12h AM/PM": "12h AM/PM", "Large language models": "<PERSON><PERSON> hình ngôn ngữ lớn", "Markdown test": "Test markdown", "Temperature: %1": "Nhiệt độ: %1", "Edit": "<PERSON><PERSON><PERSON>", "Waifus only | Excellent quality, limited quantity": "Chỉ waifus | Chất lượng xuất sắc, số lượng hạn chế", "Cheat sheet": "<PERSON><PERSON><PERSON> tra c<PERSON>u", "Current model: %1\nSet it with %2model MODEL": "Model đang chọn: %1\nChọn v<PERSON><PERSON> lệnh %2model MODEL", "Provider set to": "Đã đặt nhà cung cấp thành", "Clear": "<PERSON><PERSON><PERSON>", "GitHub": "GitHub", "App": "Ứng dụng", "Title bar": "<PERSON><PERSON> tiêu đề", "Web search": "<PERSON><PERSON><PERSON> k<PERSON> web", "Invalid model. Supported: \n```": "Model kh<PERSON><PERSON> h<PERSON><PERSON> l<PERSON>. <PERSON><PERSON><PERSON> ch<PERSON>: \n```", "Calendar": "<PERSON><PERSON><PERSON>", "Done": "<PERSON><PERSON> xong", "Monochrome": "<PERSON><PERSON> t<PERSON>", "Show regions of potential interest": "<PERSON><PERSON><PERSON> thị vùng thông minh", "Dark/Light toggle": "<PERSON>y<PERSON>n chế độ sáng/tối", "Unknown command:": "<PERSON><PERSON><PERSON> không x<PERSON>c đ<PERSON>nh:", "Allow NSFW content": "<PERSON> phép nội dung NSFW", "Closes cheatsheet on press": "<PERSON><PERSON><PERSON> bảng tra c<PERSON>u <PERSON>n", "Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.": "Chỉnh giá trị nhiệt độ (sự ngẫu nhiên) của model. <PERSON><PERSON><PERSON> trị 0-2 với Gemini, 0-1 với các model khác. Mặc định là 0.5.", "Invalid API provider. Supported: \n-": "Nhà cung cấp API không hợp lệ. Các l<PERSON> chọn: \n-", "Shell windows": "Cửa sổ của shell", "Loaded the following system prompt\n\n---\n\n%1": "<PERSON><PERSON> tải chỉ dẫn hệ thống sau đây\n\n---\n\n%1", "Clipboard": "Clipboard", "For storing API keys and other sensitive information": "<PERSON><PERSON> lưu trữ API key và các thông tin nhạy cảm khác", "Wallpaper": "<PERSON><PERSON><PERSON>", "Decorations & Effects": "<PERSON>rang tr<PERSON> & <PERSON><PERSON><PERSON>", "AI": "AI", "Large images | God tier quality, no NSFW.": "Ảnh kích thước lớn | <PERSON><PERSON><PERSON> lượng cực tốt, kh<PERSON>ng có NSFW.", "When not fullscreen": "<PERSON><PERSON> không toàn màn hình", "Resources": "<PERSON><PERSON><PERSON>", "Light": "<PERSON><PERSON><PERSON>", "Weeb": "W<PERSON><PERSON>", "Disable NSFW content": "Tắt nội dung NSFW", "OK": "OK", "Screenshot tool": "<PERSON><PERSON><PERSON> cụ chụp màn hình", "Enable": "<PERSON><PERSON><PERSON>", "Select Language": "<PERSON><PERSON><PERSON> ngôn ngữ", "System": "<PERSON><PERSON> th<PERSON>", "Emojis": "<PERSON><PERSON><PERSON>", "The current system prompt is\n\n---\n\n%1": "Chỉ dẫn hệ thống hiện tại như sau\n\n---\n\n%1", "Translator": "<PERSON><PERSON><PERSON>", "Sleep": "<PERSON><PERSON>", "Action": "<PERSON><PERSON><PERSON> đ<PERSON>", "Audio": "<PERSON><PERSON>", "Show background": "<PERSON><PERSON><PERSON>", "All-rounder | Good quality, decent quantity": "Tốt đều | <PERSON><PERSON><PERSON> lượ<PERSON> tốt, số lượ<PERSON>n", "Documentation": "<PERSON><PERSON><PERSON> l<PERSON>", "Terminal": "Terminal", "Distro": "Distro", "Clear chat history": "<PERSON><PERSON><PERSON> l<PERSON>ch sử trò chuy<PERSON>n", "Float": "<PERSON>ổ<PERSON>", "<i>No further instruction provided</i>": "<i>No further instruction provided</i>", "Choose file": "<PERSON><PERSON><PERSON>", "Set the system prompt for the model.": "Đặt chỉ dẫn hệ thống cho model.", "Unknown Title": "<PERSON><PERSON><PERSON> hát không rõ tên", "Math result": "<PERSON><PERSON><PERSON> qu<PERSON> ph<PERSON><PERSON> t<PERSON>h", "Logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "Privacy Policy": "<PERSON><PERSON><PERSON> s<PERSON>ch quyền riêng tư", "Style": "<PERSON><PERSON> c<PERSON>ch", "Borderless": "<PERSON><PERSON><PERSON><PERSON> viền", "Set API key": "Đặt API key", "Clean stuff | Excellent quality, no NSFW": "Sạch sẽ | Chất lượng xuất sắc, không có NSFW", "Experimental | Online | Google's model\nCan do a little more but doesn't search quickly": "<PERSON><PERSON><PERSON> nghiệm | <PERSON><PERSON><PERSON><PERSON> | Model của Google\n<PERSON><PERSON> thể làm nhiều hơn một chút nhưng không tìm kiếm nhanh chóng", "Toggles cheatsheet on press": "Mở/đ<PERSON>g bảng tra c<PERSON>u khi <PERSON>n", "Thinking": "<PERSON><PERSON> ngh<PERSON>", "Earbang protection": "<PERSON>ả<PERSON> vệ tai", "Advanced": "<PERSON><PERSON><PERSON> cao", "Could be better if you make a ton of typos,\nbut results can be weird and might not work with acronyms\n(e.g. \"GIMP\" might not give you the paint program)": "<PERSON><PERSON> thể tốt hơn nếu bạn gõ lệch phím nhiều,\nnhưng kết quả có thể hơi lạ và không hoạt động tốt với từ viết tắt\n(ví dụ tìm \"GIMP\" có thể không ra cái chương trình vẽ)", "Shell & utilities theming must also be enabled": "Cần Shell & công cụ cũng bật", "Desktop": "<PERSON><PERSON><PERSON> h<PERSON>", "Anime": "Anime", "Qt apps": "<PERSON><PERSON><PERSON>ng dụng Qt", "Style & wallpaper": "<PERSON><PERSON> c<PERSON> & h<PERSON><PERSON>n", "Finished tasks will go here": "Việ<PERSON> đã xong sẽ hiện ở đây", "Weather": "<PERSON><PERSON><PERSON><PERSON> tiết", "Settings": "Cài đặt", "Shell & utilities": "Shell & tiện ích", "Unfinished": "<PERSON><PERSON><PERSON> xong", "Random: Konachan": "Ngẫu nhiên: <PERSON><PERSON><PERSON>", "Pick wallpaper image on your system": "<PERSON><PERSON><PERSON> hình nền trên máy", "Volume": "<PERSON><PERSON>", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Hibernate": "<PERSON><PERSON>", "Run": "Chạy", "Keep system awake": "<PERSON><PERSON><PERSON> hệ thống bật", "To make sure this works consistently, use binditn = MODKEYS, catchall in an automatically triggered submap that includes everything.": "<PERSON><PERSON> đảm bả<PERSON> luôn hoạt động, dùng binditn = MODKEYS, catchall trong một submap luôn đ<PERSON><PERSON><PERSON> kích hoạt bao trùm mọi thứ.", "Plain rectangle": "<PERSON><PERSON><PERSON> chữ nhật", "%1 queries pending": "%1 lệnh gọi đang chờ", "Temperature set to %1": "Nhiệt độ đã được đặt thành %1", "Notifications": "<PERSON><PERSON><PERSON><PERSON> báo", "System prompt": "Chỉ dẫn hệ thống", "Hover to reveal": "Đặt chuột vào để hiện", "No": "K<PERSON>ô<PERSON>", "Bar": "Bar", "Search the web": "<PERSON><PERSON><PERSON> k<PERSON> web", "Page %1": "Trang %1", "Reboot": "Khởi động lại", "Such regions could be images or parts of the screen that have some containment.\nMight not always be accurate.\nThis is done with an image processing algorithm run locally and no AI is used.": "<PERSON><PERSON><PERSON> vùng có thể là hình ảnh hoặc phần của màn hình cớ vẻ được bao chứa.\nKhông luôn chính xác.\nSử dụng một thuật toán xử lý ảnh chạy trên m<PERSON>, không dùng AI.", "Show app icons": "<PERSON><PERSON><PERSON> bi<PERSON>u tư<PERSON>ng <PERSON>ng dụng", "Closet": "<PERSON>hiện mà ngại", "Set the current API provider": "Đặt nguồn cung cấp API", "Cancel": "<PERSON><PERSON><PERSON>", "Networking": "Mạng", "Overview": "Overview", "Search, calculate or run": "<PERSON><PERSON><PERSON>, tính hoặc chạy", "Useless buttons": "<PERSON><PERSON><PERSON> n<PERSON>t vô dụng", "Transparency": "Sự trong suốt", "Temperature must be between 0 and 2": "Nhiệt độ phải trong kho<PERSON>ng từ 0 đến 2", "Automatically suspends the system when battery is low": "Tự động ngủ khi pin thấp", "Current API endpoint: %1\nSet it with %2mode PROVIDER": "Endpoint API hiện tại: %1\nĐặt với lệnh %2mode PROVIDER", "Services": "<PERSON><PERSON><PERSON> v<PERSON>", "Reload Hyprland & Quickshell": "Tải lại Hyprland & Quickshell", "Automatic suspend": "Tự động ngủ", "illogical-impulse Welcome": "illogical-impulse - <PERSON><PERSON> ch<PERSON>o", "Interface": "<PERSON><PERSON><PERSON>", "Load chat": "<PERSON><PERSON><PERSON>c trò ch<PERSON>n", "Number show delay when pressing Super (ms)": "<PERSON><PERSON><PERSON>i gian chờ hiện số khi nhấn Super (ms)", "Clear the current list of images": "<PERSON><PERSON><PERSON> danh s<PERSON>ch hình <PERSON>nh hiện tại", "Fake screen rounding": "<PERSON><PERSON><PERSON> bo tròn màn hình", "Tip: Hide icons and always show numbers for\nthe classic illogical-impulse experience": "Mẹo: Ẩn biểu tượng và luôn hiển thị số nếu\nmuốn giống trải nghiệm illogical-impulse gốc", "Launch": "Chạy", "%1 notifications": "%1 thông báo", "%1 | Right-click to configure": "%1 | Ấn chuột phải để chỉnh", "Unknown Artist": "<PERSON><PERSON><PERSON> sĩ không xác đ<PERSON>nh", "Appearance": "<PERSON><PERSON><PERSON>", "Task Manager": "<PERSON><PERSON><PERSON><PERSON> lí <PERSON>ng dụng đang chạy", "To set an API key, pass it with the command\n\nTo view the key, pass \"get\" with the command<br/>\n\n### For %1:\n\n**Link**: %2\n\n%3": "<PERSON><PERSON> đặt API key, viết nó sau lệnh\n\nĐể xem lại, viế<PERSON> \"get\" sau lệnh<br/>\n\n### Với %1:\n\n**Link**: %2\n\n%3", "Opens cheatsheet on press": "Mở bảng tra c<PERSON>u khi <PERSON>n", "Invalid arguments. Must provide `key` and `value`.": "<PERSON><PERSON><PERSON><PERSON> không hợp lệ. c<PERSON><PERSON> c<PERSON> `key` và `value`.", "About": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "illogical-impulse": "illogical-impulse", "Help & Support": "<PERSON><PERSON><PERSON> g<PERSON>", "Enter tags, or \"%1\" for commands": "Nhập tag hoặc \"%1\" để xem các lệnh", "Format": "<PERSON><PERSON><PERSON> d<PERSON>ng", "Content": "<PERSON><PERSON><PERSON><PERSON> gốc", "Edit config": "<PERSON><PERSON><PERSON> config", "Bluetooth": "Bluetooth", "Be patient...": "<PERSON><PERSON><PERSON> tĩnh...", "Discussions": "<PERSON><PERSON><PERSON><PERSON>", "Anime boorus": "<PERSON><PERSON><PERSON> booru anime", "That didn't work. Tips:\n- Check your tags and NSFW settings\n- If you don't have a tag in mind, type a page number": "<PERSON><PERSON><PERSON> này không được. Mẹo:\n- <PERSON><PERSON><PERSON> tra tag và cài đặt NSFW\n- Nếu không nghĩ ra tag nào có thể nhập số trang", "Task description": "<PERSON><PERSON> tả công việc", "Max allowed increase": "Thay đổi tối đa", "Rows": "<PERSON><PERSON> hàng", "Switched to search mode. Continue with the user's request.": "<PERSON><PERSON> sang chế độ tìm kiếm. <PERSON><PERSON><PERSON><PERSON> tục với yêu cầu của người dùng.", "Use Levenshtein distance-based algorithm instead of fuzzy": "<PERSON><PERSON> dụng thuật toán dùng khoảng c<PERSON>ch <PERSON> thay vì fuzzy", "Copy": "Sao chép", "12h am/pm": "12h am/pm", "Unknown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "Waiting for response...": "<PERSON><PERSON> chờ phản hồi...", "Workspace": "Workspace", "Random SFW Anime wallpaper from Konachan\nImage is saved to ~/Pictures/Wallpapers": "<PERSON><PERSON>nh nền Anime SFW ngẫu nhiên từ Konachan\nẢnh được lưu vào ~/Pictures/Wallpapers", "Online via %1 | %2's model": "<PERSON>r<PERSON><PERSON> qua %1 | Model của %2", "Always show numbers": "<PERSON><PERSON><PERSON> hi<PERSON>n s<PERSON>", "or": "hoặc", "Drag or click a region • LMB: Copy • RMB: Edit": "<PERSON>éo thả hoặc chọn vùng • <PERSON><PERSON>t trái: <PERSON><PERSON> ch<PERSON><PERSON> • <PERSON><PERSON>t phải: Chỉnh sửa", "Local only": "Chỉ trên máy", "Donate": "Ủng hộ", "Online | Google's model\nGives up-to-date information with search.": "<PERSON>r<PERSON><PERSON> t<PERSON> | Model của Google\nCó thể tìm kiếm để cung cấp thông tin cập nhật.", "Run command": "<PERSON><PERSON><PERSON>", "Dotfiles": "<PERSON><PERSON><PERSON>", "Volume limit": "<PERSON><PERSON><PERSON><PERSON> hạn âm l<PERSON>", "On-screen display": "<PERSON><PERSON>/đ<PERSON> sáng", "Reboot to firmware settings": "Khởi động lại vào cài đặt firmware", "Workspaces shown": "Số workspace hiển thị", "Save": "<PERSON><PERSON><PERSON>", "The popular one | Best quantity, but quality can vary wildly": "<PERSON>ổ biến | <PERSON><PERSON> lượng tốt nhất, nh<PERSON>ng chất lượng không biết đâu vào đâu", "Save chat": "<PERSON><PERSON><PERSON> trò ch<PERSON>n", "Intelligence": "<PERSON><PERSON><PERSON> tu<PERSON>", "Translation goes here...": "<PERSON><PERSON><PERSON> dịch sẽ hiện ở đây...", "Toggle clipboard query on overview widget": "Mở/đóng tìm kiếm clipboard trên overview", "Search": "<PERSON><PERSON><PERSON>", "Timeout (ms)": "<PERSON>h<PERSON><PERSON> gian chờ (ms)", "24h": "24h", "Color picker": "<PERSON><PERSON><PERSON>u", "Save to Downloads": "Lưu vào Downloads", "No notifications": "<PERSON><PERSON><PERSON>ng có thông báo", "Game mode": "Chế độ game", "Alternatively use /dark, /light, /img in the launcher": "<PERSON><PERSON> thể dùng /dark, /light, /img trong launcher", "Info": "Thông tin", "Dock": "Dock", "Pinned on startup": "<PERSON><PERSON> khi khởi động", "Suspend at": "<PERSON>ạ<PERSON> dừng ở", "Fruit Salad": "Salad hoa quả", "API key:\n\n```txt\n%1\n```": "API key:\n\n```txt\n%1\n```", "API key set for %1": "API key đã đặt cho %1", "Not visible to model": "<PERSON><PERSON><PERSON><PERSON> hiển thị cho model", "Expressive": "<PERSON><PERSON><PERSON><PERSON>", "Enter text to translate...": "<PERSON><PERSON><PERSON><PERSON> văn bản để dịch...", "Usage": "<PERSON><PERSON><PERSON> d<PERSON>", "Message the model... \"%1\" for commands": "Hỏi model... \"%1\" để xem lệnh", "Keybinds": "<PERSON><PERSON><PERSON>", "Model set to %1": "Đã đặt model thành %1", "Scale (%)": "Tỉ lệ (%)", "Type /key to get started with online models\nCtrl+O to expand the sidebar\nCtrl+P to detach sidebar into a window": "Gõ /key để bắt đầu dùng các model trự<PERSON> tuyến\nCtrl+O để mở rộng sidebar\nCtrl+P để nhấc sidebar thành cửa sổ", "Output": "<PERSON><PERSON><PERSON> ra", "Uptime: %1": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> %1", "For desktop wallpapers | Good quality": "<PERSON> hình nền má<PERSON> t<PERSON> | <PERSON>ất lư<PERSON> tốt", "Nothing here!": "<PERSON><PERSON><PERSON>ng có gì ở đây!", "Close": "Đ<PERSON><PERSON>", "Arrow keys to navigate, Enter to select\nEsc or click anywhere to cancel": "<PERSON><PERSON><PERSON> mũi tên để chọn, Enter để xác nhận\nEsc hoặc ấn bất kỳ đâu để thoát", "Copy code": "Sao chép code", "Load prompt from %1": "Tải chỉ dẫn từ %1", "Time": "<PERSON><PERSON><PERSON><PERSON> gian", "**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\n\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key": "**Giá**: miễn phí. <PERSON><PERSON><PERSON> sách sử dụng dữ liệu tùy thuộc vào cài đặt tài khoản OpenRouter của bạn.\n\n**Hướng dẫn**: <PERSON><PERSON><PERSON> nhập vào tài khoản OpenRouter, mở Keys ở menu góc trên bên <PERSON>h<PERSON>, ấn Create API Key", "Bar style": "Phong cách bar", "Configuration": "Cài đặt", "Prefixes": "<PERSON><PERSON> tự đầu", "No API key set for %1": "Không có API key cho %1", "Add task": "<PERSON><PERSON><PERSON><PERSON> công vi<PERSON>c", "Volume mixer": "<PERSON>r<PERSON><PERSON>", "Go to source (%1)": "<PERSON><PERSON> đ<PERSON>n nguồn (%1)", "The current API used. Endpoint:": "API đang sử dụng. Endpoint:", "View Markdown source": "<PERSON><PERSON>", "Input": "<PERSON><PERSON><PERSON> vào", "Allow NSFW": "Cho phép NSFW", "Session": "Session", "Detach left sidebar into a window/Attach it back": "<PERSON><PERSON><PERSON>c sidebar tr<PERSON>i thành cửa sổ/Đặt nó lại", "Night Light": "<PERSON><PERSON><PERSON> sáng xanh", "Workspaces": "Các workspace", "Dark": "<PERSON><PERSON><PERSON>", "Base URL": "Base URL", "Hug": "Ôm", "Buttons": "<PERSON><PERSON><PERSON>", "Get the next page of results": "<PERSON><PERSON><PERSON> trang kết quả tiếp theo", "%1 Safe Storage": "<PERSON><PERSON><PERSON> trữ an toàn %1", "Color generation": "Chỉnh màu", "Select output device": "<PERSON><PERSON><PERSON> đ<PERSON>u ra", "Select input device": "<PERSON><PERSON><PERSON> đ<PERSON>u vào", "%1   •   %2 tasks": "%1   •   %2 vi<PERSON><PERSON> c<PERSON>n làm", "Online models disallowed\n\nControlled by `policies.ai` config option": "Model trực tuyến không được cho phép\n\nCài đặt bởi lựa chọn `policies.ai`", "Download complete": "<PERSON><PERSON> tải xong", "Code saved to file": "Code đã lưu vào file", "Critically low battery": "<PERSON>n r<PERSON>t thấp", "Scroll to change brightness": "Cuộn để thay đổi độ sáng", "Cloudflare WARP": "Cloudflare WARP", "Toggles bar on press": "Mở/đóng bar khi ấn", "Saved to %1": "Đã lưu vào %1", "Elements": "<PERSON><PERSON><PERSON><PERSON>", "Save chat to %1": "Lưu chat vào %1", "Connection failed. Please inspect manually with the <tt>warp-cli</tt> command": "<PERSON><PERSON><PERSON> nối không thành công. <PERSON><PERSON>y xem lại với lệnh <tt>warp-cli</tt>", "Weather Service": "<PERSON><PERSON><PERSON><PERSON> tiết", "Registration failed. Please inspect manually with the <tt>warp-cli</tt> command": "<PERSON><PERSON><PERSON> ký không thành công. H<PERSON>y xem lại với lệnh <tt>warp-cli</tt>", "Consider plugging in your device": "<PERSON><PERSON><PERSON> c<PERSON>m nguồn thiết bị của bạn", "Cloudflare WARP (1.1.1.1)": "Cloudflare WARP (1.1.1.1)", "Cannot find a GPS service. Using the fallback method instead.": "<PERSON><PERSON><PERSON><PERSON> tìm thấy dịch vụ GPS. <PERSON><PERSON> sử dụng phương pháp dự phòng.", "Opens bar on press": "Mở bar khi ấn", "Low battery": "<PERSON><PERSON>", "Scroll to change volume": "Cuộn để thay đổi âm lượng", "Please charge!\nAutomatic suspend triggers at %1": "H<PERSON>y sạc pin!\n<PERSON><PERSON> thống sẽ tự động ngủ khi pin xuống %1", "Closes bar on press": "Đóng bar khi <PERSON>n", "Mo": "T2/*keep*/", "Tu": "T3/*keep*/", "We": "T4/*keep*/", "Th": "T5/*keep*/", "Fr": "T6/*keep*/", "Sa": "T7/*keep*/", "Su": "CN/*keep*/", "Approve": "<PERSON><PERSON><PERSON>", "Set the tool to use for the model.": "<PERSON><PERSON><PERSON> công cụ để sử dụng với model.", "No API key\nSet it with /key YOUR_API_KEY": "Không có API key\nĐặt bằng /key API_KEY", "API Key": "API Key", "EasyEffects | Right-click to configure": "EasyEffects | Ấn chuột phải để chỉnh cài đặt", "API key is set": "API key đã đặt", "Invalid tool. Supported tools:\n- %1": "<PERSON><PERSON><PERSON> cụ không hợp lệ. <PERSON><PERSON><PERSON> l<PERSON> chọn:\n- %1", "Thought": "<PERSON><PERSON> <PERSON>h<PERSON>", "Current tool: %1\nSet it with %2tool TOOL": "Công cụ: %1\nĐặt bằng %2tool CÔNG_CỤ", "Edit shell config file": "Chỉnh file config của shell", "A download might be in progress": "<PERSON><PERSON> thể có tệp đang tải", "API key is set\nChange with /key YOUR_API_KEY": "API key đã đặt\nThay đổi bằng /key API_KEY", "Temperature\nChange with /temp VALUE": "<PERSON><PERSON><PERSON><PERSON> độ (độ ngẫu nhiên)\nThay đổi bằng /temp GIÁ_TRỊ", "Your package manager is running": "Package manager <PERSON><PERSON><PERSON>", "Usage: %1load CHAT_NAME": "Hướng dẫn: %1load TÊN_ĐOẠN_CHAT", "Cannot switch to search mode from %1": "<PERSON><PERSON><PERSON><PERSON> thể chuyể<PERSON> sang chế độ tìm kiếm từ %1", "UV Index": "Chỉ số UV", "Online | Google's model\nFast, can perform searches for up-to-date information": "<PERSON>r<PERSON><PERSON> t<PERSON> | Model c<PERSON>a <PERSON>, có thể tìm kiếm Google để lấy thông tin cập nhật", "Token count": "<PERSON><PERSON> l<PERSON>ng token", "Experimental | Online | Google's model\nA Gemini 2.5 Flash model optimized for cost-efficiency and high throughput.": "<PERSON><PERSON><PERSON> nghiệ<PERSON> | <PERSON><PERSON><PERSON><PERSON> | Model của Google\nModel Gemini 2.5 Flash tối ưu hóa cho hiệu quả chi phí và băng thông.", "Wallpaper parallax": "<PERSON><PERSON><PERSON>ng parallax (h<PERSON><PERSON>)", "Usage: %1tool TOOL_NAME": "Hướng dẫn: %1tool TÊN_CÔNG_CỤ", "Humidity": "<PERSON><PERSON> <PERSON><PERSON>", "Invalid tool. Supported tools: %1": "<PERSON><PERSON><PERSON> cụ không hợp lệ. <PERSON><PERSON>c l<PERSON> chọn: %1", "Sunset": "<PERSON><PERSON><PERSON> hôn", "Total token count\nInput: %1\nOutput: %2": "Số lượng token\nInput: %1\nOutput: %2", "Online | Google's model\nA Gemini 2.5 Flash model optimized for cost-efficiency and high throughput.": "<PERSON><PERSON><PERSON><PERSON> | Model c<PERSON>a Google\nModel Gemini 2.5 Flash tối ưu hóa cho hiệu quả chi phí và băng thông.", "Visibility": "<PERSON><PERSON><PERSON>", "Pressure": "<PERSON><PERSON>", "Depends on workspace": "<PERSON>ụ thuộc vào workspace", "Reject": "<PERSON><PERSON> chối", "Precipitation": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "Wind": "<PERSON><PERSON><PERSON>", "Usage: %1save CHAT_NAME": "Hướng dẫn: %1save TÊN_ĐOẠN_CHAT", "Enable EasyEffects": "Bật EasyEffects", "Night Light | Click to toggle, right-click to toggle automatic mode": "<PERSON><PERSON><PERSON>nh sáng xanh | <PERSON>n để bật/tắt, <PERSON><PERSON> chuột phải để bật/tắt chế độ tự động", "Night Light | Right-click to toggle Auto mode": "<PERSON><PERSON><PERSON> ánh sáng xanh | Ấn chuột phải để bật/tắt chế độ tự động", "No command provided": "<PERSON><PERSON><PERSON><PERSON> có lệnh nào đư<PERSON><PERSON> cung cấp", "No API key": "Không có API key", "Performance Profile toggle": "Nút Performance Profile", "Sunrise": "<PERSON><PERSON><PERSON>", "Online | Google's model\nNewer one that's slower": "<PERSON>r<PERSON><PERSON> | Model của Google\nM<PERSON>i hơn nhưng chậm hơn", "Command rejected by user": "<PERSON><PERSON><PERSON> bị từ chối bởi người dùng", "Experimental | Online | Google's model\nCan do a little more but takes an extra turn to perform search": "<PERSON><PERSON><PERSON> nghiệm | <PERSON><PERSON><PERSON><PERSON> | Model của Google\n<PERSON><PERSON> thể làm nhiều hơn một chút nhưng mất thêm một lượt để thực hiện tìm kiếm", "Depends on sidebars": "<PERSON><PERSON> thu<PERSON>c vào sidebar", "Temperature": "Nhiệt độ", "There might be a download in progress": "<PERSON><PERSON> thể có tệp đang tải", "EasyEffects": "EasyEffects", "Token count | Input: %1 | Output: %2": "Số lượng token | Input: %1 | Output: %2", "Tool set to %1": "Công cụ được đặt thành %1", "Invalid arguments. Must provide `command`.": "<PERSON><PERSON> số không hợp lệ. <PERSON><PERSON><PERSON> cung cấp `command`.", "A download is in progress": "<PERSON><PERSON> m<PERSON>t tệp đang tải", "illogical-impulse Settings": "Cài đặt illogical-impulse", "Online | Google's model\nNewer model that's slower than its predecessor but should deliver higher quality answers": "Trự<PERSON> tuyế<PERSON> | Model của Google\nMới hơn nhưng chậm hơn so với phiên bản trước nhưng nên cung cấp câu trả lời chất lượng cao hơn", "Preferred wallpaper zoom (%)": "Tỷ lệ thu phóng hình n<PERSON>n (%)", "Function Response": "<PERSON>ản hồ<PERSON> function"}