{"Output": "Вывод", "Markdown test": "Тест Markdown", "Intelligence": "ИИ", "Load chat": "Загрузить чат", "Workspaces shown": "Кол-во рабочих столов", "Style": "Стиль", "Reject": "Отклонить", "Volume": "Громкость", "Shutdown": "Завершение работы", "Fidelity": "Верность", "Switched to search mode. Continue with the user's request.": "Переключено в режим поиска. Продолжай с запросом пользователя.", "No notifications": "Нет уведомлений", "EasyEffects | Right-click to configure": "EasyEffects |  ПКМ, чтобы настроить", "Suspend at": "Переход в режим сна на", "Brightness": "Яркость", "Volume mixer": "Микшер громкости", "Discussions": "Обсуждения", "Earbang protection": "Защита от громких звуков", "Message the model... \"%1\" for commands": "Сообщение для ИИ...   \"%1\" для команд", "Decorations & Effects": "Декорации и эффекты", "Load chat from %1": "Загрузить чат из %1", "OK": "ОК", "<i>No further instruction provided</i>": "<i>Больше инструкций не предоставлено</i>", "Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.": "Установить температуру (случайность) модели. Выберите значение от 0 до 2 для <PERSON>, от 0 до 1 для других моделей. По умолчанию: 0.5", "Open file link": "Открыть ссылку на файл", "API key is set\nChange with /key YOUR_API_KEY": "API ключ установлен\nПоменять с помощью /key ВАШ_КЛЮЧ_API", "Advanced": "Продвинутые", "Title bar": "Заголовок", "Keybinds": "Шорткаты", "Alternatively use /dark, /light, /img in the launcher": "Также можно использовать /dark, /light, /img в лаунчере", "Dark/Light toggle": "Темный/светлый", "Shell & utilities": "Оболочка", "Clipboard": "Бу<PERSON>ер обмена", "Yooooo hi there": "Йооооу привет", "Show app icons": "Показывать иконки", "Save": "Сохранить", "Style & wallpaper": "Стиль и обои", "Battery": "Батарея", "Expressive": "Выразительность", "Reboot": "Перезагрузка", "AI": "ИИ", "Sleep": "Спящий режим", "Allow NSFW": "Разрешить NSFW", "Please charge!\nAutomatic suspend triggers at %1": "Подключите ПК к источнику питания!\nПереход в спящий режим на %1", "Select input device": "Выберите микрофон", "Hover to reveal": "Наведите, чтобы раскрыть", "Unknown Artist": "Неизвестный исполнитель", "Connection failed. Please inspect manually with the <tt>warp-cli</tt> command": "Ошибка подключения. Проверьте вручную командой <tt>warp-cli</tt>", "Lock": "Блокировка", "Monochrome": "Монохром", "**Pricing**: free. Data used for training.\n\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key": "**Цена**: бесплатно. Данные используются для обучения.\n\n**Инстуркции**: Войдите в аккаунт Google, разрешите AI Studio создать проект Google Cloud или что оно попросит, вернитесь и нажмите Get API key", "Online models disallowed\n\nControlled by `policies.ai` config option": "Онлайн модели запрещены\n\nУправляется опцией `policies.ai` в конфиге", "Emojis": "Эмодзи", "Wallpaper parallax": "Парал<PERSON>а<PERSON>с обоев", "Interface": "Интерфейс", "System prompt": "Системный промпт", "Edit config": "Ред. конфиг", "Page %1": "Страница %1", "Task description": "Описание задания", "Fruit Salad": "Фруктовый салад", "%1 Safe Storage": "Безопасное хранилище %1", "Distro": "<PERSON>ис<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Add": "Добавить", "Closet": "Закрытый", "Task Manager": "Системный монитор", "Configuration": "Настройка", "There might be a download in progress": "Возможно происходит скачивание", "Visibility": "Видимость", "Desktop": "Рабочий стол", "Run": "Запустить", "Sunrise": "Рассвет", "Set API key": "Задать API ключ", "Shell windows": "Окна оболочки", "Cloudflare WARP (*******)": "Cloudflare WARP (*******)", "Action": "Действие", "Elements": "Элементы", "Resources": "Ресурсы", "**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\n\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key": "**Цена**: бесплатно. Политика использования данных зависит от настроек OpenRouter.\n\n**Инструкции**: Войдите в аккаунт OpenRouter, зайдите в Keys в меню сверху справа, нажмите Create API Key", "Game mode": "Игровой режим", "Code saved to file": "Код сохранен в файл", "Online | Google's model\nGives up-to-date information with search.": "Онлайн | Модель Google\nДает точную информацию благодаря поиску", "Issues": "Проблемы", "Depends on sidebars": "Зависит от панелей", "Translation goes here...": "Здесь будет перевод...", "Bar style": "Стиль панели", "Unknown command:": "Неизвестная команда:", "Invalid API provider. Supported: \n-": "Неизвестный API провайдер. Поддерживаемые: \n-", "Clear chat history": "Очистить историю", "Run command": "Выполнить команду", "Local only": "Локальные", "Tonal Spot": "Тональное пятно", "No corresponding search model found for %1": "Не найдено поисковой модели для %1", "Content": "Контент", "Wind": "Ветер", "Total token count\nInput: %1\nOutput: %2": "Количество токенов\nВвод: %1\nВывод: %2", "Current model: %1\nSet it with %2model MODEL": "Текущая модель: %1\nСменить с помощью %2model МОДЕЛЬ", "Dark": "Темный", "Hug": "Захват", "Hibernate": "Гиб<PERSON>ррнация", "Registration failed. Please inspect manually with the <tt>warp-cli</tt> command": "Ошибка регистрации. Проверьте вручную командой <tt>warp-cli</tt>", "Calendar": "Календарь", "Save chat to %1": "Сохранить чат в %1", "Finished tasks will go here": "Здесь будут выполненные задачи", "Set the current API provider": "Задать текущего провайдера API", "Weather Service": "Сервис погоды", "Fake screen rounding": "Фейковое округление экрана", "View Markdown source": "Посмотреть исходной Markdown", "Change any time later with /dark, /light, /img in the launcher": "Изменяется в любое время с помощью /dark, /light, /img в лаунчере", "Critical warning": "Критический", "Waifus only | Excellent quality, limited quantity": "Только вайфу | Превосходное качество, ограниченное количество", "Unknown function call: %1": "Неизвестный вызов функции: %1", "Neutral": "Нейтрал", "Anime": "Аниме", "Cannot switch to search mode from %1": "Невозможно переключиться в режим поиска из %1", "Useless buttons": "Бесполезные кнопочки", "Unfinished": "Незавершенные", "Privacy Policy": "Политика конфиденциальности", "Online via %1 | %2's model": "Онлайн через %1 | Модель %2", "Large images | God tier quality, no NSFW.": "Огромные изображение | Божественное качество, нету NSFW.", "Base URL": "Базовый URL", "Not visible to model": "Невидимый для модели", "Auto": "Авто", "Might look ass. Unsupported.": "Может выглядеть хуево. Не поддерживается", "%1   •   %2 tasks": "%1   •   %2 заданий", "Search the web": "Искать в интернете", "Scroll to change brightness": "Листайте, чтобы изменить яркость", "Invalid arguments. Must provide `key` and `value`.": "Неправильные аргументы. Нужно предоставить `key` и `value`.", "Settings": "Настройки", "Show regions of potential interest": "Показывать регионы с потенциальным интересом", "Pressure": "Давление", "No": "Нет", "Such regions could be images or parts of the screen that have some containment.\nMight not always be accurate.\nThis is done with an image processing algorithm run locally and no AI is used.": "Такие регионы могут быть изображениями или части экрана, которые имеют некую ограниченность.\nМожет быть неверно.\nЭто происходит с помощью алгоритма обработки изобржений локально и ИИ не используется", "Select Language": "Выбрать Язык", "Command rejected by user": "Команда отменена пользователем", "Approve": "Разрешить", "Terminal": "<PERSON>ер<PERSON><PERSON><PERSON><PERSON>", "Search": "Поиск", "or": "или", "Experimental | Online | Google's model\nA Gemini 2.5 Flash model optimized for cost-efficiency and high throughput.": "Эксперементальная | Онлайн | Модель Google\nМодель Gemini 2.5 Flash, оптимизированная под экономию и высокую пропускную способность", "Uptime: %1": "Время работы: %1", "Save chat": "Сохранить чат", "Load prompt from %1": "Загрузить промпт из %1", "Critically low battery": "Критически низкий заряд батареи", "The hentai one | Great quantity, a lot of NSFW, quality varies wildly": "Хентай | Отличный выбор, качество сильно варьируется", "Input": "Ввод", "Borderless": "Безрамочный", "Loaded the following system prompt\n\n---\n\n%1": "Загружен следующий системный промпт\n\n---\n\n%1", "Thought": "Мысли", "Your package manager is running": "Ваш менеджер пакетов работает", "12h AM/PM": "12 ч. AM/PM", "Scale (%)": "Размер (%)", "Waiting for response...": "Ожидание ответа...", "%1 does not require an API key": "%1 не требует API ключ", "Edit": "Изменить", "Dock": "Панель задач", "Set with /mode PROVIDER": "Установить с помощью /mode ПРОВАЙДЕР", "Low warning": "Низкий", "Silent": "Выкл. звук", "Rainbow": "Радуга", "Anime boorus": "Аниме боору", "Nothing here!": "Ничего нету!", "Documentation": "Документация", "Clear": "Очистить", "Transparency": "Прозрачность", "Show background": "Показывать фон", "Info": "Инфо", "12h am/pm": "12 ч. am/pm", "Reload Hyprland & Quickshell": "Перезагрузить Hyprland и Quickshell", "%1 characters": "%1 символов", ". Notes for Zerochan:\n- You must enter a color\n- Set your zerochan username in `sidebar.booru.zerochan.username` config option. You [might be banned for not doing so](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!": ". Напоминание для Zerochan:\n- Вы должны ввести цвет\n- Установите свой юзернейм Zerochan с помощью `sidebar.booru.zerochan.username` в конфиге. Вы [можете быть забанены, если не сделаете этого](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!", "For desktop wallpapers | Good quality": "Для обоев | Отличное качество", "Type /key to get started with online models\nCtrl+O to expand the sidebar\nCtrl+P to detach sidebar into a window": "Введите /key, чтобы начать работу с онлайн моделями\nCtrl+O для расширения панели\nCtrl+P для отсоединения панели", "Close": "Закрыть", "Search, calculate or run": "Поиск, вычислить, выполнить", "Add task": "Добавить задание", "Enable": "Включить", "Temperature set to %1": "Температура установлена на %1", "No media": "Нет медиа", "Screen snip": "Скриншот", "Go to source (%1)": "Перейти к источнику (%1)", "Download": "Скачать", "Sunset": "Закат", "Weeb": "Аниме", "The current system prompt is\n\n---\n\n%1": "Текущий системный промпт\n\n---\n\n%1", "Scroll to change volume": "Листайте, чтобы изменить громкость", "Unknown Title": "Неизвестное Название", "Policies": "Политики", "Disable NSFW content": "Отключить NSFW контент", "The current API used. Endpoint:": "Текущий используемый API. Точка входа:", "Humidity": "Влажность", "Cheat sheet": "Шпаргалка", "Translator": "Переводчик", "Be patient...": "Подождите...", "Experimental | Online | Google's model\nCan do a little more but takes an extra turn to perform search": "Эксперементальная | Онлайн | Модель Google\nМожет сделать чуть больше, но нужен дополнительный запрос для поиска", "Audio": "Аудио", "Preferred wallpaper zoom (%)": "Предпочитаемый зум обоев (%)", "Model set to %1": "Установлена модель %1", "Enter text to translate...": "Введите текст для перевода...", "Use Levenshtein distance-based algorithm instead of fuzzy": "Использовать алгоритм, основанный на расстоянии Левенштейна,\n вместо нечёткого сопоставления.", "Depends on workspace": "Зависит от пространства", "All-rounder | Good quality, decent quantity": "Универсальный | Хорошее качество и количество", "Timeout (ms)": "Таймаут (мс)", "Plain rectangle": "Обычный прямоугольник", "No API key\nSet it with /key YOUR_API_KEY": "Нет API ключа.\nУстановите его с помощью /key ВАШ_КЛЮЧ_API", "Center title": "Центрировать название", "Buttons": "Кнопки", "Copy code": "Скопировать код", "Precipitation": "Осадки", "The popular one | Best quantity, but quality can vary wildly": "Популярный | Огромный выбор, но качество сильно варьируется", "Temperature: %1": "Температура: %1", "Qt apps": "Qt", "Delete": "Удалить", "Saved to %1": "Сохранено в %1", "Temperature\nChange with /temp VALUE": "Температура\nИзмените с помощью /temp ЗНАЧЕНИЕ", "App": "Приложение", "Bluetooth": "Bluetooth", "Current API endpoint: %1\nSet it with %2mode PROVIDER": "Текущий API: %1\nИзменить с помощью %2mode ПРОВАЙДЕР", "Cancel": "Отменить", "Clean stuff | Excellent quality, no NSFW": "Чистый контент | Превосходное качество, нету NSFW", "Wallpaper": "Обои", "Provider set to": "Провайдер установлен на", "Weather": "Погода", "24h": "24 ч.", "Show next time": " Показать в следующий раз", "Unknown": "Неизвестно", "Launch": "Запустить", "Overview": "Обзор", "Help & Support": "Помощь и поддержка", "Night Light | Right-click to toggle Auto mode": "Ночной свет | ПКМ для переключения Авто режима", "Web search": "Веб-поиск", "Cannot find a GPS service. Using the fallback method instead.": "Не удалось найти GPS сервис. Используем запасный вариант.", "Large language models": "Большие языковые модели", "Dotfiles": "Дотфайлы", "When not fullscreen": "Когда не в полноэкранном режиме", "Screenshot tool": "Инструмент скриншотов", "Get the next page of results": "Получить резултаты следующей страницы", "Drag or click a region • LMB: Copy • RMB: Edit": "Проведите или кликните на регион • ЛКМ: Скопировать • ПКМ: Редактировать", "Material palette": "Материальная палитра", "Columns": "Столбцы", "Bar": "Панель", "Max allowed increase": "Макс. рост", "Always show numbers": "Всегда показывать номера", "%1 notifications": "%1 уведомлений", "Rows": "Ряды", "Invalid arguments. Must provide `command`.": "Неправильный аргумент. Нужно предоставить `command`.", "System": "Система", "Shell & utilities theming must also be enabled": "Расцветка оболочки также должна быть включена.", "%1 queries pending": "%1 запросов в очереди", "Copy": "Скопировать", "Logout": "Выход", "Pinned on startup": "Закреплена на запуске", "%1 | Right-click to configure": "%1 | ПК<PERSON>, чтобы настроить", "Donate": "Задонатить", "Temperature must be between 0 and 2": "Температура должна быть между 0 и 2", "Session": "Сессия", "Mic toggle": "Перекл. микрофон", "Reboot to firmware settings": "Перезагрузиться в настройки BIOS/UEFI", "Low battery": "Низкий", "Usage": "Использование", "Notifications": "Уведомления", "Consider plugging in your device": "Задумайтесь о подключении ПК к источнику питания", "Cloudflare WARP": "Cloudflare WARP", "Automatically suspends the system when battery is low": "Автоматически переходит в режим сна когда заряд низкий", "Services": "Сервисы", "Thinking": "Ду<PERSON><PERSON><PERSON>", "Color generation": "Генерация цветов", "Number show delay when pressing Super (ms)": "Задержка показа номеров при нажатии Super (мс)", "illogical-impulse Welcome": "Добро пожаловать в illogical-impulse", "User agent (for services that require it)": "User agent (для сервисов, которым он нужен)", "Appearance": "Вн<PERSON><PERSON>ний вид", "On-screen display": "On-screen display", "Download complete": "Загрузка завершена", "Time": "Время", "Float": "Поверх", "Pick wallpaper image on your system": "Выберите изображение для обоев на своём ПК", "Prevents abrupt increments and restricts volume limit": "Предотвращает резкие увеличения громкости и закрепляет лимит громоксти", "Unknown Album": "Неизвестный Альбом", "Math result": "Результат вычисления", "Random SFW Anime wallpaper from Konachan\nImage is saved to ~/Pictures/Wallpapers": "Случайные SFW обои с Konachan\nИзображение сохраняются в ~/Изображения/Wallpapers", "Could be better if you make a ton of typos,\nbut results can be weird and might not work with acronyms\n(e.g. \"GIMP\" might not give you the paint program)": "Может быть лучше, если вы сделаете много ошибок,\nно результаты могут быть странными и могут не работать с сокращениями (т.е. \"GIMP\" не выдаст программу для рисования)", "Select output device": "Выберите динамики", "Set the system prompt for the model.": "Задать системный промпт для этой модели.", "Choose file": "Выберите файл", "Choose model": "Выберите модель", "Tip: Hide icons and always show numbers for\nthe classic illogical-impulse experience": "Подсказка: Включите \"Показывать иконик \" и \"Всегда показывать номера\"\nдля классического опыта illogical-impulse", "Yes": "Да", "Local Ollama model | %1": "Локальная модель Ollama | %1", "API key set for %1": "API ключ установлен на %1", "Format": "Формат", "Colors & Wallpaper": "Цвета и Обои", "Note: turning off can hurt readability": "Подсказка: отключение может повредить читабельность", "illogical-impulse": "illogical-impulse", "Automatic suspend": "Авто-сон", "Random: Konachan": "Рандом: <PERSON><PERSON><PERSON>", "Workspace": "Рабочее пространство", "About": "О системе", "Color picker": "Пипетка", "Report a Bug": "Сообщить об ошибке", "Volume limit": "Лимит громкости", "GitHub": "GitHub", "Prefixes": "Префиксы", "Done": "Готово", "Invalid model. Supported: \n```": "Неправильная модель. Доступны: \n```", "To set an API key, pass it with the command\n\nTo view the key, pass \"get\" with the command<br/>\n\n### For %1:\n\n**Link**: %2\n\n%3": "Чтобы установить API ключ, добавьте ее к команде\n\nЧтобы просмотреть ключ, добавьте \"get\" к команде<br/>\n\n### Для %1:\n\n**Ссылка**: %2\n\n%3", "UV Index": "УФ-индекс", "Clear the current list of images": "Очистить список изображений", "No audio source": "Нет источников аудио", "API key:\n\n```txt\n%1\n```": "API ключ:\n\n```txt\n%1\n```", "For storing API keys and other sensitive information": "Для хранения API ключей и другой чувствительной информации", "Arrow keys to navigate, Enter to select\nEsc or click anywhere to cancel": "Стрелочки для навигации, Enter для выбора\nEsc или клик везде чтобы отменить", "Networking": "Сеть", "Keep system awake": "Дер<PERSON><PERSON><PERSON>ь ПК включённым", "Polling interval (ms)": "Интервал опроса (мс)", "To Do": "Зада<PERSON>и", "Workspaces": "Рабочие пространства", "That didn't work. Tips:\n- Check your tags and NSFW settings\n- If you don't have a tag in mind, type a page number": "Это не сработало. Подсказки:\n- Проверьте теги и настройки NSFW\n- Если на уме нету тегов, введите номер страницы", "Jump to current month": "Перейти к текущему месяцу", "Enter tags, or \"%1\" for commands": "Введите теги, или \"%1\" для команд", "No API key set for %1": "API ключ не установлен для %1", "Allow NSFW content": "Разрешить NSFW контент", "Save to Downloads": "Сохранить в загрузки", "Light": "Светлый", "Keyboard toggle": "Экранная клавиатура", "Night Light": "Night Light", "We": "Ср/*keep*/", "Mo": "Пн/*keep*/", "Su": "Вс/*keep*/", "Th": "Чт/*keep*/", "Tu": "Вт/*keep*/", "Experimental | Online | Google's model\nCan do a little more but doesn't search quickly": "Эксперементальная | Онлайн | Модель Google\nМожет немного больше но не ищет очень быстро", "Sa": "Сб/*keep*/", "Chain of Thought": "Цепочка мыслей", "Fr": "Пт/*keep*/", "Usage: %1load CHAT_NAME": "Использование: %1load ИМЯ_ЧАТА", "Tool set to %1": "Установлен инструмент %1", "Set the tool to use for the model.": "Установите инструмент для этой модели.", "Invalid tool. Supported tools:\n- %1": "Неправильный инструмент. Доступны:\n- %1", "Usage: %1tool TOOL_NAME": "Использование: %1tool ИНСТРУМЕНТ", "Performance Profile toggle": "Профили производительности", "Usage: %1save CHAT_NAME": "Использование: %1save ИМЯ_ЧАТА", "Online | Google's model\nA Gemini 2.5 Flash model optimized for cost-efficiency and high throughput.": "Онлайн | Модель Google\nМодель Gemini 2.5 Flash оптимизирована под меньшие затраты и высокую производительнность", "Online | Google's model\nNewer model that's slower than its predecessor but should deliver higher quality answers": "Онлайн | Модель Google\nНовая модель, которая медленее, чем ее предшественник, но выдает лучшее качество", "Online | Google's model\nFast, can perform searches for up-to-date information": "Онлайн | Модель Google\nБыстрая, может выполнять поиск актуальной информации", "Current tool: %1\nSet it with %2tool TOOL": "Текущий инструмент: %1\nИзменяется с помощью %2tool ИНСТРУМЕНТ"}