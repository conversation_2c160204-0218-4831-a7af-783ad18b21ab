{"Mo": "Пн/*keep*/", "Tu": "Вт/*keep*/", "We": "Ср/*keep*/", "Th": "Чт/*keep*/", "Fr": "Пт/*keep*/", "Sa": "Сб/*keep*/", "Su": "Нд/*keep*/", "%1 characters": "%1 символів", "**Pricing**: free. Data use policy varies depending on your OpenRouter account settings.\n\n**Instructions**: Log into OpenRouter account, go to Keys on the topright menu, click Create API Key": "**Вартість**: безкоштовно. Політика використання даних залежить від параметрів облікового запису OpenRouter.\n\n**Інструкції**: Увійдіть в обліковий запис OpenRouter, перейдіть до розділу «Keys» у верхньому правому меню, натисніть «Create API Key»", "**Pricing**: free. Data used for training.\n\n**Instructions**: Log into Google account, allow AI Studio to create Google Cloud project or whatever it asks, go back and click Get API key": "**Вартість**: безкоштовно. Дані використовуються для тренування.\n\n**Інструкції**: Увійдіть в обліковий запис Google, дозвольте AI Studio створити проект Google Cloud або те, що вона попросить, поверніться назад і натисніть Get API key", ". Notes for Zerochan:\n- You must enter a color\n- Set your zerochan username in `sidebar.booru.zerochan.username` config option. You [might be banned for not doing so](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!": ". Примітки для Zerochan:\n- Ви повинні ввести колір\n- Встановіть ваше ім'я користувача zerochan у параметрі конфігурації `sidebar.booru.zerochan.username`. Якщо ви цього не зробите, вас [може бути заблоковано](https://www.zerochan.net/api#:~:text=The%20request%20may%20still%20be%20completed%20successfully%20without%20this%20custom%20header%2C%20but%20your%20project%20may%20be%20banned%20for%20being%20anonymous.)!", "<i>No further instruction provided</i>": "<i>Подальших інструкцій не надано</i>", "Action": "Дія", "Add": "Додати", "Add task": "Додати завдання", "All-rounder | Good quality, decent quantity": "Універсальний | Хороша якість, пристойна кількість", "Allow NSFW": "Дозволити NSFW", "Allow NSFW content": "Дозволити NSFW вміст", "Anime": "Ані<PERSON>е", "Anime boorus": "Аніме боору", "App": "Програма", "Arrow keys to navigate, Enter to select\nEsc or click anywhere to cancel": "Стрілки для навігації, Enter для вибору\nНатисніть Esc або будь де щоб скасувати", "Bluetooth": "Bluetooth", "Brightness": "Яскравість", "Cancel": "Скасувати", "Cheat sheet": "Шпаргалка", "Choose model": "Виберіть модель", "Clean stuff | Excellent quality, no NSFW": "Чистий вміст | Відмінна якість, без NSFW", "Clear": "Очистити", "Clear chat history": "Очистити історію чату", "Clear the current list of images": "Очистити поточний список картинок", "Close": "Закрити", "Copy": "Копіювати", "Copy code": "Копіювати код", "Delete": "Видалити", "Desktop": "Стільниця", "Disable NSFW content": "Вимкнути NSFW вміст", "Done": "Готово", "Download": "Завант<PERSON><PERSON><PERSON>ти", "Edit": "Редагувати", "Enter text to translate...": "Введіть текст щоб перекласти...", "Finished tasks will go here": "Завершені завдання зберігаються тут", "For desktop wallpapers | Good quality": "На шпалери | Хороша якість", "For storing API keys and other sensitive information": "Для зберігання API ключів та іншої конфіденційної інформація", "Game mode": "Ігровий режим", "Get the next page of results": "Наступна сторінка результатів", "Hibernate": "Сплячий режим", "Input": "<PERSON><PERSON><PERSON><PERSON>", "Intelligence": "Інтелект", "Interface": "Інтерфейс", "Invalid arguments. Must provide `key` and `value`.": "Неправильні аргументи. Повинні бути вказані `key` та `value`.", "Jump to current month": "Перейти до поточного місяця", "Keep system awake": "Тримати систему в режимі очікування", "Large images | God tier quality, no NSFW.": "Великі зображення | Божественна якість, без NSFW.", "Large language models": "Великі мовні моделі", "Launch": "Пуск", "Lock": "Блокувати", "Logout": "Вийти", "Markdown test": "Тест Markdown", "Math result": "Результат обчислень", "No audio source": "Немає джерела аудіо", "No media": "Немає медіа", "No notifications": "Немає сповіщень", "Not visible to model": "Не видно для моделі", "Nothing here!": "Тут нічого!", "Notifications": "Сповіщення", "OK": "Гаразд", "Open file link": "Відкрити лінк до файлу", "Output": "Вивід", "Reboot": "Перезапуск", "Reboot to firmware settings": "Перезапуск в параметри UEFI/BIOS", "Reload Hyprland & Quickshell": "Перезавантажити Hyprland та Quickshell", "Run": "Виконати", "Run command": "Виконати команду", "Save": "Зберегти", "Save to Downloads": "Зберегти в Завантаження", "Search": "По<PERSON><PERSON>к", "Search the web": "Шукати в інтернеті", "Search, calculate or run": "Шук<PERSON><PERSON><PERSON>, обчислювати або запускати", "Select Language": "Вибра<PERSON>и мову", "Session": "Сесія", "Set API key": "Вказати ключ API", "Set temperature (randomness) of the model. Values range between 0 to 2 for Gemini, 0 to 1 for other models. Default is 0.5.": "Задати температуру (випадковість) моделі. Значення в діапазоні від 0 до 2 для <PERSON>, від 0 до 1 для інших моделей. Значення за замовчуванням 0.5.", "Set the current API provider": "Встановити поточного провайдера API", "Shutdown": "Вимкнути", "Silent": "Тиша", "Sleep": "Сон", "System": "Система", "Task Manager": "Менеджер завдань", "Task description": "О<PERSON>и<PERSON> завдання", "Temperature must be between 0 and 2": "Температура має бути між 0 та 2", "The hentai one | Great quantity, a lot of NSFW, quality varies wildly": "Хентай | Велика кількість, багато NSFW, якість сильно варіюється", "The popular one | Best quantity, but quality can vary wildly": "Популярний | Найкраща кількість, але якість може сильно варіюватись", "Thinking": "Замислився", "Translation goes here...": "Переклад буде тут...", "Translator": "Перек<PERSON><PERSON><PERSON><PERSON><PERSON>", "Unfinished": "Незавершений", "Unknown": "Невідмий", "Unknown Album": "Невідомий Альбои", "Unknown Artist": "Невідомий Виконавець", "Unknown Title": "Невідома назва", "View Markdown source": "Дивитися джерело Markdown", "Volume": "Гу<PERSON>ність", "Volume mixer": "Мік<PERSON><PERSON>р гучності", "Waifus only | Excellent quality, limited quantity": "Лише вайфу | Відмінна якість, обмежена кількість", "Waiting for response...": "Чекаємо відповідь...", "Workspace": "Простір", "Invalid API provider. Supported: \n-": "Неправильний провайдер API. Підтримується: \n-", "Unknown command:": "Невідома команда:", "Type /key to get started with online models\nCtrl+O to expand the sidebar\nCtrl+P to detach sidebar into a window": "Введіть /key, щоб почати роботу з онлайн моделями\nCtrl+O, щоб розгорнути бічну панель\nCtrl+P, щоб прибрати бічну панель у вікно", "Provider set to": "Провайдер виставлений на", "Invalid model. Supported: \n```": "Неправельна модель. Підтримується: \n```", "That didn't work. Tips:\n- Check your tags and NSFW settings\n- If you don't have a tag in mind, type a page number": "Це не спрацювало. Поради:\n- Перевірте свої теги та параметри NSFW\n- Якщо ви не знаєте тега, введіть номер сторінки", "Switched to search mode. Continue with the user's request.": "Перейшли в режим пошуку. Продовження пошуку за запитом користувача.", "Settings": "Параметри", "Save chat": "Зберегти чат", "Load chat": "Заванта<PERSON>ити чат", "or": "або", "Set the system prompt for the model.": "Встановіть системний запит для моделі.", "To Do": "Зробити", "Calendar": "Календар", "Advanced": "Розширені", "About": "Про", "Services": "Сервіси", "Style": "Стиль", "Edit config": "Редагувати конфігурацію", "Colors & Wallpaper": "Кольори та Шпалери", "Light": "Світла", "Dark": "Темна", "Material palette": "Палітра кольор<PERSON>в", "Fidelity": "Вірність", "Fruit Salad": "Фруктовий салат", "Alternatively use /dark, /light, /img in the launcher": "Або використовуйте /dark, /light, /img у лаунчері", "Fake screen rounding": "Фальшиві заокруглення екрану", "When not fullscreen": "Коли не на весь екран", "Choose file": "Вибрати файл", "Random SFW Anime wallpaper from Konachan\nImage is saved to ~/Pictures/Wallpapers": "Випадкові SFW аніме шпалери від <PERSON>\nЗображення збережено до ~/Pictures/Wallpapers", "Be patient...": "Потерпіть...", "Decorations & Effects": "Декорації та ефекти", "Tonal Spot": "Тональна пляма", "Shell windows": "Вікна оболонки", "Auto": "Авто", "Wallpaper": "Шпалери", "Content": "Вміст", "Title bar": "Заголовок", "Transparency": "Прозорість", "Expressive": "Виразний", "Yes": "Так", "Enable": "Увімкнути", "Rainbow": "Веселка", "Might look ass. Unsupported.": "Виглядатиме дурновато. Не підтримується.", "Monochrome": "Монохромний", "Random: Konachan": "Випадково: Konachan", "Center title": "Назва по центру", "Neutral": "Нейтральний", "Pick wallpaper image on your system": "Виберіть зображення шпалер на вашому комп'ютері", "No": "Ні", "AI": "ШІ", "Local only": "Лише локально", "Policies": "Політика", "Weeb": "Віа<PERSON>у", "Closet": "Шафа", "Bar style": "Стиль заголовку", "Show next time": "Показати пізніше", "Usage": "Використання", "Plain rectangle": "Звичайний квадрат", "Useless buttons": "Безкорисні кнопки", "GitHub": "GitHub", "Style & wallpaper": "Стиль та шпалери", "Configuration": "Конфігурація", "Change any time later with /dark, /light, /img in the launcher": "Змініть будь-коли пізніше за допомогою /dark, /light, /img у лаунчері", "Keybinds": "Комбінації клавіш", "Float": "Плаваюче", "Hug": "Обійми", "Yooooo hi there": "Йоооо, привіт", "illogical-impulse Welcome": "illogical-impulse Вітаємо", "Info": "Інфо", "Volume limit": "Обмеження гучності", "Prevents abrupt increments and restricts volume limit": "Запобігає різкому збільшенню та обмежує ліміт гучності", "Resources": "Ресурси", "12h am/pm": "12г AM/PM", "Base URL": "Базовий лінк", "Audio": "Ау<PERSON><PERSON><PERSON>", "Networking": "Мережування", "Format": "Формат", "Time": "<PERSON><PERSON><PERSON>", "Battery": "Батарея", "Prefixes": "Префікси", "Emojis": "Емодзі", "Earbang protection": "Захист навушників", "Automatically suspends the system when battery is low": "Автоматично призупиняє роботу системи, коли батарея розряджається", "Automatic suspend": "Автома<PERSON><PERSON><PERSON>на зупинка", "Suspend at": "Зупиняти на", "Max allowed increase": "Максимально допустимий приріст", "Web search": "Пошук в інтернеті", "Polling interval (ms)": "Інтервал між опитуваннями (мс)", "Clipboard": "<PERSON>у<PERSON>ер обміну", "Low warning": "Незначні попередження", "24h": "24г", "Use Levenshtein distance-based algorithm instead of fuzzy": "Використовуйте алгоритм на основі відстані Левенштейна замість нечіткого", "System prompt": "Системний запит", "12h AM/PM": "12г AM/PM", "Could be better if you make a ton of typos,\nbut results can be weird and might not work with acronyms\n(e.g. \"GIMP\" might not give you the paint program)": "Було б краще, якби ви зробили купу помилок,\nале результати можуть бути дивними і не працювати з абревіатурами\n(наприклад, «GIMP» може не вивести програму для малювання).", "Critical warning": "Критичні попередження", "User agent (for services that require it)": "User agent (для сервісів де це потрібно)", "Such regions could be images or parts of the screen that have some containment.\nMight not always be accurate.\nThis is done with an image processing algorithm run locally and no AI is used.": "Такими областями можуть бути зображення або частини екрана, які мають певні обмеження.\nМоже бути не завжди точним.\nЦе робиться за допомогою алгоритму обробки зображень, запущеного локально, і не використовується ШІ.", "Note: turning off can hurt readability": "Примітка: вимкнення може погіршити читабельність", "Workspaces shown": "Показані простори", "Dark/Light toggle": "Перемикач Світлої/Темної", "Dock": "Лоток", "Weather": "Погода", "Pinned on startup": "Прикріплено до запуску", "Tip: Hide icons and always show numbers for\nthe classic illogical-impulse experience": "Порада: приховуйте іконки і завжди показуйте цифри для класичного досвіду illogical-impulse", "Appearance": "Вигляд", "Always show numbers": "Завжди показувати номери", "Buttons": "Кнопки", "Keyboard toggle": "Перемикання клавіатури", "Scale (%)": "Розмір (%)", "Overview": "Огляд", "Rows": "Рядки", "Borderless": "Без меж", "Screenshot tool": "Інструмент створення скріншотів", "Number show delay when pressing Super (ms)": "Затримка відображення номера при натисканні клавіші Super (мс)", "Timeout (ms)": "Тайм-аут (ms)", "Show app icons": "Показувати іконки програм", "Workspaces": "Простори", "Columns": "Стовбці", "On-screen display": "Екранний дисплей", "Screen snip": "Скріншот", "Mic toggle": "Перемикач мікрофону", "Hover to reveal": "Наведіть курсор, щоб відкрити", "Bar": "Панель", "Show background": "Показувати задній фон", "Show regions of potential interest": "Показати регіони потенційного інтересу", "Color picker": "<PERSON>и<PERSON><PERSON><PERSON> кольору", "Help & Support": "Допомога та підтримка", "Discussions": "Дискусії", "Color generation": "Гене<PERSON><PERSON><PERSON><PERSON><PERSON> кольорів", "Dotfiles": "Дотфайли", "Distro": "<PERSON>ис<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Privacy Policy": "Політика Конфіденційності", "Documentation": "Документація", "Shell & utilities theming must also be enabled": "Тему оболонки та утиліт також слід увімкнути", "illogical-impulse": "illogical-impulse", "Donate": "<PERSON><PERSON><PERSON><PERSON>", "Terminal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Shell & utilities": "Оболонка та утиліти", "Qt apps": "Програми Qt", "Report a Bug": "Повідомити про помилку", "Issues": "Проблеми", "Drag or click a region • LMB: Copy • RMB: Edit": "Перетягніть або клацніть регіон - LMB: Копіювати - RMB: Редагувати", "Current model: %1\nSet it with %2model MODEL": "Поточна модель: %1\nЗадати її за допомогою %2model МОДЕЛЬ", "Message the model... \"%1\" for commands": "Написати моделі... «%1» для команд", "No API key set for %1": "Немає вказаного API ключач для %1", "Loaded the following system prompt\n\n---\n\n%1": "Завантажився наступний системний запит\n\n---\n\n%1", "%1 | Right-click to configure": "%1 | ПКМ для налаштування", "API key set for %1": "ключ API вказано для %1", "Online via %1 | %2's model": "Онлайн через %1 | %2's модель", "Current API endpoint: %1\nSet it with %2mode PROVIDER": "Поточна кінцева точка API: %1\nВстановіть її за допомогою %2mode ПРОВАЙТЕР", "Go to source (%1)": "До джерела (%1)", "Temperature set to %1": "Температура виставлена на %1", "Enter tags, or \"%1\" for commands": "Напишіть тег або \"%1\" для команд", "%1 queries pending": "%1 запитів на розгляді", "API key:\n\n```txt\n%1\n```": "ключ API:\n\n```txt\n%1\n```", "Uptime: %1": "Активно: %1", "%1 Safe Storage": "%1 Надійне зберігання", "%1 does not require an API key": "%1 не потребує API ключа", "Temperature: %1": "температура: %1", "Model set to %1": "Модель встановлена на %1", "Page %1": "Сторінка %1", "Local Ollama model | %1": "Локальна модель Оллама | %1", "The current system prompt is\n\n---\n\n%1": "Поточний запит системи\n\n---\n\n%1", "Unknown function call: %1": "Невідомий виклик функції: %1", "%1 notifications": "%1 сповіщень", "Load chat from %1": "Завантажено чат з %1", "Load prompt from %1": "Завантажено запит з %1", "Save chat to %1": "Зберегти чат до %1", "Weather Service": "Сервіс погоди", "Cannot find a GPS service. Using the fallback method instead.": "Не вдається знайти службу GPS. Замість цього використовується резервний метод.", "Critically low battery": "Критичний рівень заряду батареї", "Select output device": "Виберіть вихідний пристрій", "Code saved to file": "Код збережений у файл", "Online models disallowed\n\nControlled by `policies.ai` config option": "Онлайн-моделі заборонено\n\nКерується параметром конфігурації `policies.ai`", "Scroll to change volume": "Прокрутіть, щоб змінити гучність", "Elements": "Елементи", "%1   •   %2 tasks": "%1   •   %2 завдань", "Download complete": "Завантаження завершено", "Please charge!\nAutomatic suspend triggers at %1": "Будь ласка, зарядіть! \nАвтоматичне призупинення спрацьовування при %1", "Cloudflare WARP": "Cloudflare WARP", "Cloudflare WARP (1.1.1.1)": "Cloudflare WARP (1.1.1.1)", "Scroll to change brightness": "Прокрутіть щоб змінити яскравість", "Connection failed. Please inspect manually with the <tt>warp-cli</tt> command": "Помилка зєднання. Перевірте самостійно за допомогою команди <tt>warp-cli</tt>", "Select input device": "Виберіть вхідний пристрій", "Registration failed. Please inspect manually with the <tt>warp-cli</tt> command": "Помилка реєстрації. Перевірте самостійно за допомогою команди <tt>warp-cli</tt>", "Consider plugging in your device": "Подумайте про те, щоб підключити свій пристрій", "Low battery": "Низький заряд батареї", "Saved to %1": "Збережено до %1", "Sunset": "За<PERSON><PERSON>д сонця", "UV Index": "Індекс UV", "Humidity": "Вологість", "Wind": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sunrise": "Світанок", "Pressure": "Тиск", "Visibility": "Видимість", "Precipitation": "Опади", "No API key\nSet it with /key YOUR_API_KEY": "Немає API ключа\nВстановіть його можна командою /key YOUR_API_KEY", "Your package manager is running": "Ваш пакетний менеджер запущено", "Night Light | Right-click to toggle Auto mode": "Нічне світло | ПКМ щоб увімкнути автоматичний режим", "Gives the model search capabilities (immediately)": "Надає можливість пошуку моделі (негайно)", "Depends on workspace": "Залежно від простору", "Invalid arguments. Must provide `command`.": "Неправельні параметри. Потрібно вказати `command`.", "Temperature\nChange with /temp VALUE": "Температура\nЗмінити можна командою /temp ЗНАЧЕННЯ", "Online | Google's model\nGoogle's state-of-the-art multipurpose model that excels at coding and complex reasoning tasks.": "Онлайн | Модель Google\nСучасна багатоцільова модель Google, яка чудово справляється з кодуванням і складними завданнями на міркування.", "EasyEffects | Right-click to configure": "EasyEffects | ПКМ щоб налаштувати", "Thought": "Думка", "Online | Google's model\nA Gemini 2.5 Flash model optimized for cost-efficiency and high throughput.": "Онлайн | Модель Google \nМодель Gemini 2.5 Flash оптимізована для економічної ефективності та високої пропускної здатності.", "API key is set\nChange with /key YOUR_API_KEY": "API ключ встановлено\nЗмінити можна командою /key YOUR_API_KEY", "Current tool: %1\nSet it with %2tool TOOL": "Поточний інструмент: %1\nВстановіть його командою %2tool TOOL", "**Instructions**: Log into Mistral account, go to Keys on the sidebar, click Create new key": "**Інструкції**: Увійдіть в обліковий зап<PERSON><PERSON>, перейдіть до розділу «Keys» на бічній панелі, натисніть «Create new key»", "Usage: %1tool TOOL_NAME": "Використання: %1tool TOOL_NAME", "Online | Google's model\nFast, can perform searches for up-to-date information": "Он<PERSON><PERSON><PERSON>н | модель Google \nШвидкий, може здійснювати пошук актуальної інформації", "Approve": "Схвалити", "Preferred wallpaper zoom (%)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> маштаб шпалер (%)", "Performance Profile toggle": "Перемикач профілю продуктивності", "Total token count\nInput: %1\nOutput: %2": "Загальна кількість токенів\nВхідні: %1\nВихідні: %2", "Wallpaper parallax": "Обємні шпалери", "Invalid tool. Supported tools:\n- %1": "Неправельний інструмент. Підтримуються:\n- %1", "Usage: %1load CHAT_NAME": "Виокристання: %1load CHAT_NAME", "Reject": "Від<PERSON><PERSON><PERSON>ити", "Usage: %1save CHAT_NAME": "Використання: %1save CHAT_NAME", "Set the tool to use for the model.": "Вкажіть інструмент для роботи з моделью", "Online | %1's model | Delivers fast, responsive and well-formatted answers. Disadvantages: not very eager to do stuff; might make up unknown function calls": "Онлайн | %1's модель | Надає швидкі, чуйні та добре відформатовані відповіді. Недоліки: не дуже охоче виконує завдання; може вигадувати виклики невідомих функцій", "Depends on sidebars": "Залежно від бокових панелей", "Command rejected by user": "Команда відхилена користувачем", "There might be a download in progress": "Можливо, триває завантаження", "Disable tools": "Вимкнути інструмент", "Tool set to: %1": "Інструмет вказано: %1", "Commands, edit configs, search.\nTakes an extra turn to switch to search mode if that's needed": "Команди, редагування конфігурацій, пошук.\nВиконує додаткову дію для переходу в режим пошуку, якщо це потрібно", "To set an API key, pass it with the %4 command\n\nTo view the key, pass \"get\" with the command<br/>\n\n### For %1:\n\n**Link**: %2\n\n%3": "Щоб задати ключ API, передайте його командою %4\n\nЩоб переглянути ключ, передайте \"get\" командою<br/>\n\n### Для %1:\n\n**Лінк**: %2\n\n%3", "Online | Google's model\nNewer model that's slower than its predecessor but should deliver higher quality answers": "Он<PERSON><PERSON><PERSON>н | Модель Google\nНовіша модель, яка повільніша за свою попередницю, але має надавати якісніші відповіді"}