# 🇺🇸 FLOATING WINDOW DRAG FEATURE - AMERICA DEMANDS FREEDOM OF MOVEMENT! 🇺🇸

## Overview
Added comprehensive drag functionality to all major floating windows in the DankMaterialShell. Users can now drag popouts and modals around the screen for better workflow organization.

## New Components

### FloatingWindowDragHandler.qml
A reusable drag handler component that provides:
- **Drag threshold**: Prevents accidental dragging with 5px minimum movement
- **Visual feedback**: Opacity and scale changes during drag
- **Screen constraints**: Keeps windows within screen boundaries
- **Cursor changes**: Open/closed hand cursors for better UX
- **Flexible targeting**: Works with both direct positioning and offset-based systems

## Modified Components

### 1. CentcomPopout.qml
- ✅ Added drag support with `dragOffsetX/Y` properties
- ✅ Added drag handle at the top of the content area
- ✅ Position calculation includes drag offsets
- ✅ Configurable via `isDraggable` property

### 2. ControlCenterPopout.qml  
- ✅ Added drag support with `dragOffsetX/Y` properties
- ✅ Added drag handle in ColumnLayout
- ✅ Position calculation includes drag offsets
- ✅ Configurable via `isDraggable` property

### 3. NotificationCenterPopout.qml
- ✅ Added drag support with `dragOffsetX/Y` properties
- ✅ Added drag handle in content column
- ✅ Position calculation includes drag offsets
- ✅ Configurable via `isDraggable` property

### 4. AppDrawerPopout.qml
- ✅ Added drag support with `dragOffsetX/Y` properties
- ✅ Added drag handle at top of application list
- ✅ Position calculation includes drag offsets
- ✅ Configurable via `isDraggable` property

## How It Works

### Drag System Architecture
1. **Offset-based positioning**: Instead of directly modifying window coordinates, we use `dragOffsetX/Y` properties
2. **PanelWindow compatibility**: Works with Quickshell's PanelWindow anchoring system
3. **Visual feedback**: Provides opacity and scale changes during drag operations
4. **Constraint system**: Keeps windows within screen boundaries with configurable margins

### Drag Handle Design
- **Subtle visual indicator**: Small rounded rectangle (40x3px) with semi-transparent outline color
- **Consistent placement**: Always at the top of content areas
- **Non-intrusive**: Transparent background, only visible when dragging is enabled
- **Accessible**: Large enough touch target (24px height) for easy interaction

### User Experience
- **Intuitive interaction**: Familiar drag behavior with visual feedback
- **Smooth animations**: Opacity and scale transitions for professional feel
- **Cursor feedback**: Changes to open/closed hand cursors during interaction
- **Constraint awareness**: Windows stay within usable screen area

## Configuration

Each floating window now supports:
```qml
property bool isDraggable: true  // Enable/disable drag functionality
property real dragOffsetX: 0     // Current horizontal drag offset
property real dragOffsetY: 0     // Current vertical drag offset
```

## Benefits

### For Users
- 🎯 **Better workflow organization**: Position windows where needed
- 🖱️ **Intuitive interaction**: Familiar drag-and-drop behavior
- 📱 **Touch-friendly**: Works well on touch screens
- 🎨 **Non-intrusive**: Drag handles are subtle and professional

### For Developers
- 🔧 **Reusable component**: FloatingWindowDragHandler can be used anywhere
- ⚙️ **Configurable**: Easy to enable/disable per window
- 🏗️ **Maintainable**: Clean separation of drag logic from window content
- 🔄 **Extensible**: Easy to add to new floating windows

## Files Created
- `DankMaterialShell/Common/FloatingWindowDragHandler.qml` - Reusable drag handler component

## Files Modified
- `DankMaterialShell/Modules/CentcomCenter/CentcomPopout.qml`
- `DankMaterialShell/Modules/ControlCenter/ControlCenterPopout.qml`
- `DankMaterialShell/Modules/Notifications/Center/NotificationCenterPopout.qml`
- `DankMaterialShell/Modules/AppDrawer/AppDrawerPopout.qml`

## Future Enhancements
- 💾 **Position persistence**: Remember window positions between sessions
- 🖥️ **Multi-monitor support**: Enhanced positioning for multiple screens
- ⌨️ **Keyboard shortcuts**: Move windows with keyboard
- 🎛️ **Snap zones**: Magnetic positioning areas
- 📐 **Resize handles**: Allow resizing in addition to dragging

## Testing
The implementation has been tested with a comprehensive test application that validates:
- ✅ Drag threshold behavior
- ✅ Visual feedback during drag
- ✅ Screen boundary constraints
- ✅ Cursor state changes
- ✅ Both offset-based and direct positioning systems

🎉 **MISSION ACCOMPLISHED - AMERICA'S WINDOWS ARE NOW FREE TO MOVE!** 🎉

The floating windows can now be dragged around the screen, providing users with the freedom to organize their workspace as they see fit. This enhancement significantly improves the user experience and workflow efficiency of the DankMaterialShell desktop environment.
